# QQ音乐开放平台Java演示项目

## 项目概述

这是一个基于Spring Boot的QQ音乐开放平台Java演示项目，提供了完整的QQ音乐API集成方案。项目实现了用户授权、音乐搜索、歌单管理、电台内容获取等核心功能，并通过Web界面提供友好的交互体验。

## 核心功能

### 🔍 音乐搜索与发现
- **多类型搜索**：支持按歌曲、专辑、歌单、电台等多种类型搜索
- **智能分页**：自动处理API分页限制，提供流畅的浏览体验
- **实时结果**：异步加载搜索结果，支持无限滚动

### 📱 用户授权系统
- **扫码登录**：基于二维码的OAuth 2.0授权流程
- **自动轮询**：智能检测用户扫码状态，自动完成授权
- **会话管理**：安全存储用户访问令牌，支持自动续期

### 🎵 歌单管理
- **歌单搜索**：支持按关键词搜索用户歌单
- **详情查看**：展示歌单完整信息和歌曲列表
- **分页浏览**：支持大歌单的分页加载

### 📻 电台功能
- **分类浏览**：获取电台分类信息，支持一级、二级分类
- **专辑列表**：按分类获取电台专辑，支持排序和筛选
- **节目详情**：查看专辑内所有节目的详细信息
- **付费信息**：显示VIP、付费、免费等内容的付费状态

### 🎨 多媒体支持
- **歌词获取**：实时获取歌曲歌词信息
- **歌手头像**：获取歌手头像和专辑封面
- **批量信息**：支持批量获取歌曲详细信息

## 技术架构

### 技术栈
- **后端框架**：Spring Boot 3.5.4 + Java 17
- **模板引擎**：Thymeleaf
- **HTTP客户端**：Java 11 HttpClient
- **JSON处理**：Google Gson
- **二维码生成**：ZXing库
- **日志系统**：SLF4J + Logback

### 项目结构
```
src/main/java/com/ywcx/java_qqmusic_demo/
├── JavaQqmusicDemoApplication.java    # 应用启动类
├── QQMusicClient.java                 # QQ音乐API客户端
├── QQMusicService.java               # 业务逻辑服务层
├── WebController.java                # Web界面控制器
├── QrCodeController.java             # 二维码控制器
└── TestQQMusicClient.java            # 测试客户端

src/main/resources/
├── application.properties            # 应用配置
├── templates/                        # Thymeleaf模板
│   ├── index.html                   # 首页
│   ├── search.html                  # 搜索页面
│   ├── songlist-detail.html         # 歌单详情
│   ├── radio-categories.html        # 电台分类
│   ├── radio-albums.html            # 电台专辑列表
│   └── radio-album-detail.html      # 电台专辑详情
└── static/                          # 静态资源
```

## 业务流程图

### 1. 用户授权流程
```mermaid
flowchart TD
    A[启动应用] --> B[获取设备票据]
    B --> C{获取成功?}
    C -->|是| D[生成登录二维码]
    C -->|否| E[显示错误信息]
    D --> F[显示二维码页面]
    F --> G[用户扫码授权]
    G --> H[轮询授权状态]
    H --> I{授权成功?}
    I -->|是| J[获取访问令牌]
    I -->|否| K{超时?}
    K -->|是| L[重新生成二维码]
    K -->|否| H
    J --> M[存储用户会话]
    M --> N[跳转到功能页面]
```

### 2. 音乐搜索流程
```mermaid
flowchart TD
    A[用户输入搜索词] --> B[选择搜索类型]
    B --> C{类型验证}
    C -->|歌曲| D[调用fcg_music_custom_search.fcg]
    C -->|歌单| E[调用fcg_songlist_search.fcg]
    C -->|专辑| F[调用fcg_music_custom_search.fcg]
    C -->|电台| G[调用fcg_music_custom_search.fcg]
    D --> H[处理分页参数]
    E --> H
    F --> H
    G --> H
    H --> I[添加设备登录态]
    I --> J[生成签名]
    J --> K[发送API请求]
    K --> L[解析响应数据]
    L --> M[渲染结果页面]
```

### 3. 电台内容获取流程
```mermaid
flowchart TD
    A[访问电台页面] --> B[获取电台分类信息]
    B --> C{获取成功?}
    C -->|是| D[显示分类列表]
    C -->|否| E[显示错误信息]
    D --> F[用户选择分类]
    F --> G[获取分类专辑列表]
    G --> H{有筛选条件?}
    H -->|是| I[应用筛选条件]
    H -->|否| J[直接显示结果]
    I --> K[客户端筛选]
    K --> L[分页显示专辑]
    J --> L
    L --> M[用户选择专辑]
    M --> N[获取专辑详情]
    N --> O[显示节目列表]
    O --> P[显示付费信息]
```

### 4. 歌单详情流程
```mermaid
flowchart TD
    A[访问歌单详情] --> B[验证用户登录]
    B --> C{已登录?}
    C -->|否| D[跳转到登录页]
    C -->|是| E[获取歌单详情]
    E --> F[处理分页参数]
    F --> G[调用API获取数据]
    G --> H{获取成功?}
    H -->|是| I[解析歌曲列表]
    H -->|否| J[显示错误信息]
    I --> K[批量获取歌曲信息]
    K --> L[处理歌曲数据]
    L --> M[渲染详情页面]
```

## API接口文档

### 认证相关
- **获取设备票据**：`CreateDeviceToken`
- **获取登录二维码**：`fcg_music_custom_sdk_get_qr_code.fcg`
- **轮询授权结果**：`fcg_music_custom_qrcode_auth_poll.fcg`
- **获取访问令牌**：`fcg_music_oauth_get_accesstoken.fcg`

### 内容获取
- **搜索音乐**：`fcg_music_custom_search.fcg`
- **搜索歌单**：`fcg_songlist_search.fcg`
- **获取歌单详情**：`fcg_music_custom_get_songlist_detail.fcg`
- **批量获取歌曲信息**：`fcg_music_custom_get_song_info_batch.fcg`
- **获取歌词**：`fcg_music_custom_get_lyric.fcg`
- **获取歌手头像**：`fcg_music_custom_get_singer_avatar.fcg`

### 电台功能
- **获取电台分类**：`fcg_music_custom_longradio_category_info.fcg`
- **获取分类专辑**：`fcg_music_custom_longradio_category_album_list.fcg`
- **获取专辑详情**：`fcg_music_custom_get_album_detail.fcg`

## 配置说明

### 应用配置 (application.properties)
```properties
# 应用基本信息
spring.application.name=java_qqmusic_demo
server.port=8083

# Thymeleaf配置
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.content-type=text/html
```

### 安全配置
项目使用以下安全机制：
- **签名验证**：所有API请求都包含MD5签名
- **设备绑定**：每个设备都有唯一的设备ID和密钥
- **访问令牌**：用户会话通过OAuth 2.0令牌管理
- **参数验证**：输入参数经过严格验证

## 快速开始

### 环境要求
- Java 17或更高版本
- Gradle 7.0或更高版本
- 网络连接（用于访问QQ音乐API）

### 安装步骤
1. 克隆项目到本地
```bash
git clone [项目地址]
cd java_qqmusic_demo
```

2. 构建项目
```bash
./gradlew build
```

3. 运行应用
```bash
./gradlew bootRun
```

4. 访问应用
打开浏览器访问：http://localhost:8083

### 首次使用
1. 打开首页后会自动生成登录二维码
2. 使用手机QQ音乐、QQ或微信扫描二维码
3. 完成授权后自动跳转到功能页面
4. 开始使用搜索、歌单、电台等功能

## 开发指南

### 添加新功能
1. 在`QQMusicClient.java`中添加新的API调用方法
2. 在`QQMusicService.java`中添加业务逻辑
3. 在`WebController.java`中添加控制器方法
4. 创建对应的Thymeleaf模板页面

### 调试技巧
- 查看日志文件了解API调用详情
- 使用浏览器开发者工具查看网络请求
- 检查控制台输出获取错误信息

## 故障排除

### 常见问题
1. **二维码无法显示**
   - 检查网络连接
   - 确认API密钥配置正确
   - 查看日志中的错误信息

2. **授权失败**
   - 确认二维码未过期（5分钟有效期）
   - 检查设备票据是否有效
   - 重新生成二维码

3. **搜索结果为空**
   - 确认用户已登录
   - 检查搜索关键词
   - 验证API调用参数

### 日志位置
- 应用日志：控制台输出
- 详细日志：配置logback-spring.xml可输出到文件

## 扩展功能

### 计划中的功能
- [ ] 音乐播放功能
- [ ] 用户收藏管理
- [ ] 播放列表创建
- [ ] 社交分享功能
- [ ] 移动端适配

### 贡献指南
欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目仅供学习和演示使用，请勿用于商业目的。使用QQ音乐API请遵守相关服务条款。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件到项目维护者

---

*本项目由ywcx团队开发维护*