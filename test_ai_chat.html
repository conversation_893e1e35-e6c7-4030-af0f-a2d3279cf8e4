<!DOCTYPE html>
<html>
<head>
    <title>AI对话流式输出测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>AI对话流式输出测试</h1>
    <div>
        <input type="text" id="messageInput" placeholder="输入消息..." style="width: 300px;">
        <button onclick="testStream()">测试流式输出</button>
    </div>
    <div id="output" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px; white-space: pre-wrap;"></div>

    <script>
        function testStream() {
            const message = document.getElementById('messageInput').value;
            if (!message) return;
            
            const output = document.getElementById('output');
            output.innerHTML = '开始流式输出...\n';
            
            const eventSource = new EventSource(`http://localhost:8083/api/chat/stream?message=${encodeURIComponent(message)}`);
            
            eventSource.onmessage = function(event) {
                output.innerHTML += event.data;
            };
            
            eventSource.addEventListener('done', function(event) {
                output.innerHTML += '\n\n[流式输出完成]';
                eventSource.close();
            });
            
            eventSource.onerror = function(event) {
                output.innerHTML += '\n[连接错误]';
                eventSource.close();
            };
        }
    </script>
</body>
</html>