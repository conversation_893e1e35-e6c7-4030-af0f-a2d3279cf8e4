spring.application.name=java_qqmusic_demo
server.port=8083

# æ¥å¿éç½®
logging.level.com.ywcx.java_qqmusic_demo=INFO
logging.level.org.springframework=INFO

# Thymeleaféç½®

spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.content-type=text/html

# Moonshot AIéç½®ï¼å¯éï¼ä¹å¯ä»¥éè¿ç¯å¢åéMOONSHOT_API_KEYè®¾ç½®ï¼
moonshot.api.key=sk-9YyCdWZEiwb5rtZx4inMHBz3ESBywEZAgzOTPPYf2dpAhQTL