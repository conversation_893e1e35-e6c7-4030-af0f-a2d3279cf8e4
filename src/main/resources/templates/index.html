<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ音乐登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .qr-container {
            margin: 20px 0;
        }
        .qr-code {
            max-width: 100%;
            height: auto;
        }
        .instructions {
            margin: 20px 0;
            color: #666;
        }
        .loading {
            display: none;
        }
        .nav-buttons {
            display: none;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        .nav-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .nav-button.active {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover:not(.active) {
            background-color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ音乐扫码登录</h1>
        <div class="qr-container">
            <img th:if="${qrCodeUrl}" th:src="${qrCodeUrl}" alt="QQ音乐登录二维码" class="qr-code">
        </div>
        <div class="instructions">
            <p>请使用QQ音乐、QQ或微信扫描上方二维码进行登录</p>
            <p>授权有效期：5分钟</p>
        </div>
        <div class="loading" id="loading">
            <p>正在等待扫码授权...</p>
        </div>
        
        <div class="nav-buttons" id="navButtons">
            <a href="/search" class="nav-button active">音乐搜索</a>
            <a href="/radio" class="nav-button">电台分类</a>
        </div>

    </div>

    <script th:inline="javascript">
        /*<![CDATA[*/
        var qrCodeUrl = /*[[${qrCodeUrl}]]*/ '';
        if (qrCodeUrl) {
            // 每隔5秒检查一次登录状态
            setInterval(function() {
                fetch('/check-login')
                    .then(response => response.json())
                    .then(data => {
                        if (data.loggedIn) {
                            // 登录成功后自动跳转到主页面
                            window.location.href = '/radio';
                        }
                    })
                    .catch(error => {
                        console.error('检查登录状态失败:', error);
                    });
            }, 5000);
        }
        /*]]>*/

    </script>
</body>
</html>