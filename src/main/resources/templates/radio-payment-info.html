<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电台付费信息说明</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #31c27c;
            border-bottom: 2px solid #31c27c;
            padding-bottom: 10px;
        }
        .payment-type {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #31c27c;
        }
        .payment-type h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .payment-type p {
            margin: 5px 0;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .status-free { background: #d4edda; color: #155724; }
        .status-vip { background: #fff3cd; color: #856404; }
        .status-paid { background: #f8d7da; color: #721c24; }
        .status-trial { background: #d1ecf1; color: #0c5460; }
        
        .example {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .example h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .back-link {
            text-align: center;
            margin-top: 30px;
        }
        .back-link a {
            color: #31c27c;
            text-decoration: none;
            padding: 10px 20px;
            border: 2px solid #31c27c;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .back-link a:hover {
            background: #31c27c;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>电台付费类型说明</h1>
        
        <div class="section">
            <h2>专辑付费模式 (lr_pay_type)</h2>
            
            <div class="payment-type">
                <h3><span class="status-badge status-free">免费</span>lr_pay_type = 0 或 null</h3>
                <p>完全免费收听，无需付费或VIP</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-vip">VIP免费</span>lr_pay_type = 1</h3>
                <p>QQ音乐VIP用户可免费收听</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-paid">整集购买</span>lr_pay_type = 2</h3>
                <p>需要购买整个专辑才能收听</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-paid">单集购买</span>lr_pay_type = 3</h3>
                <p>可以单独购买每一集收听</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-paid">包月订阅</span>lr_pay_type = 4</h3>
                <p>通过包月订阅方式收听</p>
            </div>
        </div>
        
        <div class="section">
            <h2>节目付费状态</h2>
            
            <div class="payment-type">
                <h3><span class="status-badge status-free">免费</span>playable = 1 且 vip = 0</h3>
                <p>可以直接播放，无需付费</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-vip">VIP专享</span>vip = 1 且 playable = 0</h3>
                <p>需要VIP会员才能播放</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-paid">付费</span>playable = 0</h3>
                <p>需要付费购买才能播放</p>
            </div>
            
            <div class="payment-type">
                <h3><span class="status-badge status-trial">可试听</span>try_begin 和 try_end 有值</h3>
                <p>付费内容提供试听片段</p>
                <div class="example">
                    <h4>试听示例：</h4>
                    <p>try_begin: 0, try_end: 30000 → 可试听前30秒</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>系统处理逻辑</h2>
            <div class="example">
                <h4>付费状态判断优先级：</h4>
                <p>1. 如果 playable = 1 且 vip = 0 → <span class="status-badge status-free">免费</span></p>
                <p>2. 如果 vip = 1 且 playable = 0 → <span class="status-badge status-vip">VIP专享</span></p>
                <p>3. 如果 playable = 0 且有试听 → <span class="status-badge status-trial">付费(可试听)</span></p>
                <p>4. 如果 playable = 0 → <span class="status-badge status-paid">付费</span></p>
                <p>5. 其他情况 → <span class="status-badge">可播放</span></p>
            </div>
        </div>
        
        <div class="back-link">
            <a href="/radio">返回电台分类</a>
        </div>
    </div>
</body>
</html>