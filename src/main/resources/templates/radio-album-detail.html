<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ音乐 - 电台专辑详情</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .nav-button.active {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover:not(.active) {
            background-color: #dee2e6;
        }
        .album-header {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .album-cover {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
        }
        .album-info {
            flex: 1;
        }
        .album-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .album-meta {
            color: #666;
            margin-bottom: 15px;
        }
        .album-desc {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .songs-table {
            width: 100%;
            border-collapse: collapse;
        }
        .songs-table th,
        .songs-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .songs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .songs-table tr:hover {
            background-color: #f8f9fa;
        }
        .song-title {
            font-weight: bold;
        }
        .song-info {
            color: #666;
            font-size: 14px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        .page-link {
            padding: 8px 12px;
            background-color: #e9ecef;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
        }
        .page-link.active {
            background-color: #007bff;
            color: white;
        }
        .page-link:hover:not(.active) {
            background-color: #dee2e6;
        }
        .empty-list {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-buttons">
            <a href="/search" class="nav-button">音乐搜索</a>
            <a href="/radio" class="nav-button active">电台分类</a>
        </div>
        
        <!-- 成功状态显示 -->
        <div th:if="${result != null and result.ret == 0}">
            <div class="album-header">
                <img th:src="${result.album_pic_150x150 != null ? result.album_pic_150x150 : result.album_pic}" 
                     alt="专辑封面" 
                     class="album-cover"
                     onerror="this.src='https://y.gtimg.cn/mediastyle/global/img/album_300.png'">
                <div class="album-info">
                    <div class="album-title" th:text="${result.album_name}">专辑标题</div>
                    <div class="album-meta">
                        <div>发布时间: <span th:text="${result.get('public_time')}">-</span></div>
                        <div>播放量: <span th:if="${result.get('listen_num') != null}" th:text="${#numbers.formatDecimal(result.get('listen_num'), 1, 'COMMA', 0, 'POINT')}">0</span>
                        <span th:if="${result.get('listen_num') == null}">-</span></div>
                        <div>节目总数: <span th:text="${result.get('total_num')}">0</span></div>
                        <div>分类: <span th:text="${result.get('long_audio_tag')}">-</span></div>
                        <div>出品方: <span th:text="${result.get('company_name')}">-</span></div>
                        <div th:if="${result.get('lr_pay_type') == 1}" style="color: #ff6b35;">VIP专享</div>
                        <div th:if="${result.get('lr_pay_type') == 2}" style="color: #ff6b35;">付费购买</div>
                    </div>
                    <div class="album-desc" th:text="${result.album_desc}">专辑简介</div>
                </div>
            </div>
            
            <h2>节目列表</h2>
            <!-- 有节目时显示表格 -->
            <div th:if="${result.songlist != null and not #lists.isEmpty(result.songlist)}">
                <table class="songs-table">
                    <thead>
                        <tr>
                            <th>节目名称</th>
                            <th>时长</th>
                            <th>播放量</th>
                            <th>VIP</th>
                            <th>试听</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="song : ${result.songlist}">
                            <td>
                                <div class="song-title" th:text="${song.get('song_name')}">节目标题</div>
                                <div class="song-info">
                                    ID: <span th:text="${song.get('song_id')}">-</span> | 
                                    MID: <span th:text="${song.get('song_mid')}">-</span>
                                </div>
                            </td>
                            <td>
                                <span th:if="${song.get('song_play_time') != null}" th:text="${song.get('song_play_time')} + 's'">时长</span>
                                <span th:if="${song.get('song_play_time') == null}">-</span>
                            </td>
                            <td>
                                <span th:if="${song.get('listen_num') != null}" th:text="${#numbers.formatDecimal(song.get('listen_num'), 1, 'COMMA', 0, 'POINT')}">播放量</span>
                                <span th:if="${song.get('listen_num') == null}" th:text="'-'">播放量</span>
                                <span th:if="${song.get('listen_num') != null}" class="play-count-formatted" th:attr="data-count=${song.get('listen_num')}"></span>
                            </td>
                            <td>
                                <span th:if="${song.vip == 1}" style="color: #ff6b35;">是</span>
                                <span th:if="${song.vip != 1}" style="color: #28a745;">否</span>
                            </td>
                            <td>
                                <span th:if="${song.get('try_begin') != null and song.get('try_end') != null}">
                                    <span th:text="${song.get('try_begin') / 1000}">0</span>s - 
                                    <span th:text="${song.get('try_end') / 1000}">30</span>s
                                </span>
                                <span th:if="${song.get('try_begin') == null or song.get('try_end') == null}">-</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页控制 -->
                <div class="pagination">
                    <a th:if="${page > 0}" 
                       th:href="@{/radio/album/{albumId}(albumId=${albumId}, albumMid=${albumMid}, page=${page - 1}, sortBy=${sortBy}, favState=${favState})}" 
                       class="page-link">上一页</a>
                    <span class="page-link active" th:text="${page + 1}">1</span>
                    <a th:if="${result.songlist != null and #lists.size(result.songlist) == 50}" 
                       th:href="@{/radio/album/{albumId}(albumId=${albumId}, albumMid=${albumMid}, page=${page + 1}, sortBy=${sortBy}, favState=${favState})}" 
                       class="page-link">下一页</a>
                </div>
                
                <!-- 排序控制 -->
                <div style="margin-top: 20px; text-align: center;">
                    <a th:href="@{/radio/album/{albumId}(albumId=${albumId}, albumMid=${albumMid}, page=0, sortBy=-3, favState=${favState})}" 
                       th:class="${sortBy == -3 ? 'nav-button active' : 'nav-button'}">正序</a>
                    <a th:href="@{/radio/album/{albumId}(albumId=${albumId}, albumMid=${albumMid}, page=0, sortBy=3, favState=${favState})}" 
                       th:class="${sortBy == 3 ? 'nav-button active' : 'nav-button'}">倒序</a>
                </div>
            </div>
            
            <!-- 无节目时显示 -->
            <div th:if="${result.songlist == null or #lists.isEmpty(result.songlist)}" class="empty-list">
                <p>暂无节目</p>
            </div>
        </div>
        
        <!-- 错误状态显示 -->
        <div th:if="${result == null or result.ret != 0}" class="error-message">
            <p th:text="${result != null ? result.msg : '获取专辑详情失败'}">获取专辑详情失败</p>
            <a href="/radio" class="nav-button" style="margin-top: 20px;">返回电台分类</a>
        </div>
    </div>

    <script th:inline="javascript">
        /*<![CDATA[*/
        function playSong(songMid, songId) {
            // 这里可以添加播放逻辑
            console.log('播放歌曲:', songMid, songId);
        }

        // 格式化大数字显示（播放量）
        function formatPlayCount(count) {
            if (!count || count === 0) return '0';
            
            if (count >= 100000000) {
                // 亿级别
                return (count / 100000000).toFixed(1) + '亿';
            } else if (count >= 10000) {
                // 万级别
                return (count / 10000).toFixed(1) + '万';
            } else {
                // 普通数字，添加千位分隔符
                return count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }
        }

        // 页面加载完成后格式化所有播放量
        document.addEventListener('DOMContentLoaded', function() {
            const playCountElements = document.querySelectorAll('.play-count-formatted');
            playCountElements.forEach(function(element) {
                const count = parseInt(element.getAttribute('data-count'));
                if (count) {
                    element.textContent = formatPlayCount(count);
                }
            });
        });
        /*]]>*/
    </script>
</body>
</html>