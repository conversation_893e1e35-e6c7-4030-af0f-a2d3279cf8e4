<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ音乐 - 电台分类</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .nav-button.active {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover:not(.active) {
            background-color: #dee2e6;
        }
        .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .category-item {
            flex: 1 1 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #fafafa;
        }
        .category-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .subcategory-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .subcategory-item {
            padding: 5px 10px;
            background-color: #e9ecef;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .subcategory-item:hover {
            background-color: #007bff;
            color: white;
        }
        .album-container {
            margin-top: 30px;
        }
        .album-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 10px;
        }
        .album-item {
            flex: 1 1 150px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            background-color: #fff;
        }
        .album-cover {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 3px;
        }
        .album-title {
            font-size: 14px;
            margin-top: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .sort-options {
            margin: 10px 0;
        }
        .sort-option {
            margin-right: 15px;
            cursor: pointer;
            color: #007bff;
        }
        .sort-option.active {
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>电台分类</h1>
        <a href="/" class="back-link">← 返回首页</a>
        
        <div class="nav-buttons">
            <a href="/search" class="nav-button">音乐搜索</a>
            <a href="/radio" class="nav-button active">电台分类</a>
            <a href="/ai-chat" class="nav-button">🤖 AI 对话</a>
        </div>
        
        <!-- 登录状态检查和调试信息 -->
        <div id="loginStatus" style="color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0; display: none;">
            <p>⚠️ 请先扫码登录后再查看电台分类</p>
            <p><a href="/" style="color: #007bff;">点击返回首页进行登录</a></p>
        </div>
        <div th:if="${categoryResult == null}" style="color: red; padding: 10px; border: 1px solid red; margin: 10px 0;">
            <p>错误：categoryResult 为空，可能是用户未登录</p>
            <p><a href="/" style="color: #007bff;">点击返回首页进行登录</a></p>
        </div>
        <div th:if="${categoryResult != null and categoryResult.get('ret') != null and categoryResult.get('ret') != 0}" style="color: red; padding: 10px; border: 1px solid red; margin: 10px 0;">
            <p>API返回错误: <span th:text="${categoryResult.get('ret')}"></span> - <span th:text="${categoryResult.get('msg') != null ? categoryResult.get('msg') : '未知错误'}"></span></p>
        </div>
        <div th:if="${categoryResult != null and categoryResult.get('ret') != null and categoryResult.get('ret') == 0 and (categoryResult.get('categoryLists') == null or #lists.isEmpty(categoryResult.get('categoryLists')))}" style="color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;">
            <p>分类列表为空</p>
        </div>
        
        <!-- 正确的数据渲染 -->
        <div class="category-list" th:if="${categoryResult != null and categoryResult.get('ret') != null and categoryResult.get('ret') == 0 and categoryResult.get('categoryLists') != null and !#lists.isEmpty(categoryResult.get('categoryLists'))}">
            <div class="category-item" th:each="category : ${categoryResult.get('categoryLists')}">
                <div class="category-title" th:text="${category.get('title') != null ? category.get('title') : '未知分类'}"></div>
                <div class="subcategory-list">
                    <div class="subcategory-item" 
                         th:each="subCategory : ${category.get('subCategory')}"
                         th:text="${subCategory.get('subtitle') != null ? subCategory.get('subtitle') : '未知子分类'}"
                         th:attr="data-fst-id=${category.get('fstId')}, data-snd-id=${subCategory.get('sndId')}"
                         onclick="loadAlbums(this)">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="album-container" id="albumContainer" style="display: none;">
            <h2 id="subcategoryTitle"></h2>
            <div class="sort-options">
                <span class="sort-option active" data-sort="0" onclick="changeSort(this)">最新</span>
                <span class="sort-option" data-sort="1" onclick="changeSort(this)">最热</span>
            </div>
            <div class="album-list" id="albumList"></div>
            <div class="loading" id="loading">加载中...</div>
        </div>
    </div>

    <script th:inline="javascript">
        /*<![CDATA[*/
        function loadAlbums(element) {
            const fstId = element.getAttribute('data-fst-id');
            const sndId = element.getAttribute('data-snd-id');
            const title = encodeURIComponent(element.textContent);
            
            // 确保ID是整数类型
            const fstIdInt = parseInt(fstId, 10);
            const sndIdInt = parseInt(sndId, 10);
            
            // 检查ID是否有效
            if (isNaN(fstIdInt) || isNaN(sndIdInt)) {
                alert('无效的分类ID');
                return;
            }
            
            // 跳转到专辑列表页面，传递分类标题
            window.location.href = `/radio/albums?fstId=${fstIdInt}&sndId=${sndIdInt}&title=${title}`;
        }

        /*]]>*/
    </script>
</body>
</html>