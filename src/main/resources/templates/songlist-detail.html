<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歌单详情 - QQ音乐</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 30px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .songlist-info {
            text-align: left;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .songlist-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .songlist-meta {
            color: #666;
            margin-bottom: 10px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .song-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            text-align: left;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .song-item:last-child {
            border-bottom: none;
        }
        .song-info {
            flex-grow: 1;
        }
        .song-name {
            font-weight: bold;
            color: #333;
        }
        .singer-name {
            color: #666;
            margin-left: 10px;
        }
        .song-actions {
            flex-shrink: 0;
        }
        .view-details-btn, .view-lyric-btn, .view-avatar-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 5px;
        }
        .view-details-btn:hover, .view-lyric-btn:hover, .view-avatar-btn:hover {
            background-color: #0056b3;
        }
        .no-results {
            text-align: center;
            color: #999;
            padding: 20px;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination a {
            display: inline-block;
            padding: 5px 10px;
            margin: 0 5px;
            text-decoration: none;
            border: 1px solid #ddd;
            color: #007bff;
        }
        .pagination a:hover {
            background-color: #e9ecef;
        }
        .pagination .current {
            background-color: #007bff;
            color: white;
        }
        .error {
            color: red;
            text-align: center;
            padding: 20px;
        }
        
        /* 歌曲详情弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 5px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover,
        .close:focus {
            color: black;
        }
        
        .song-detail-info {
            text-align: left;
        }
        
        .song-detail-info h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .detail-row {
            margin: 10px 0;
        }
        
        .detail-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        
        .play-url {
            color: #007bff;
            text-decoration: none;
        }
        
        .play-url:hover {
            text-decoration: underline;
        }
        
        .lyric-content {
            white-space: pre-line;
            line-height: 1.6;
            font-family: 'Courier New', Courier, monospace;
        }
        
        .avatar-img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/search" class="back-link">← 返回搜索</a>
        
        <div th:if="${result.ret != null and result.ret == 0}">
            <h1>歌单详情</h1>
            
            <div class="songlist-info">
                <div class="songlist-title" th:text="${result.diss_title ?: '未知歌单'}">歌单标题</div>
                <div class="songlist-meta">
                    <span th:text="${'歌曲数: ' + (result.total_num ?: 0)}">歌曲数</span>
                </div>
            </div>
            
            <div th:if="${result.song_list == null or result.song_list.isEmpty()}" class="no-results">
                <p>该歌单暂无歌曲</p>
            </div>
            
            <div th:unless="${result.song_list == null or result.song_list.isEmpty()}">
                <div class="song-item" th:each="song : ${result.song_list}">
                    <div class="song-info">
                        <span class="song-name" th:text="${song.song_name ?: '未知歌曲'}">歌曲名</span>
                        <span class="singer-name" th:text="${song.singer_name ?: '未知歌手'}">歌手名</span>
                    </div>
                    <div class="song-actions">
                        <!-- 根据接口文档，song_type为3或13时可以播放，其他类型直接置灰 -->
                        <button class="view-details-btn" 
                                th:attr="data-songmid=${song.song_mid}, data-singermid=${song.singer_mid}"
                                th:disabled="${song.song_type != null and song.song_type != 3 and song.song_type != 13}">
                            详情
                        </button>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination">
                    <a th:if="${page > 0}" th:href="@{/songlist/{dissid}(dissid=${dissid}, page=${page - 1})}">上一页</a>
                    <span th:text="${'第 ' + (page + 1) + ' 页'}"></span>
                    <a th:if="${(page + 1) * 30 < result.total_num}" th:href="@{/songlist/{dissid}(dissid=${dissid}, page=${page + 1})}">下一页</a>
                </div>
            </div>
        </div>
        
        <div th:if="${result.ret != null and result.ret != 0}" class="error">
            <p>获取歌单详情失败: <span th:text="${result.msg}">错误信息</span></p>
        </div>
    </div>
    
    <!-- 歌曲详情模态框 -->
    <div id="songDetailModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="song-detail-info">
                <h3>歌曲详情</h3>
                <div id="songDetailContent">
                    <div class="detail-section">
                        <h4>基本信息</h4>
                        <div id="basicInfo"></div>
                    </div>
                    
                    <div class="detail-section">
                        <h4>歌手头像</h4>
                        <div id="avatarInfo" class="avatar-container"></div>
                    </div>
                    
                    <div class="detail-section">
                        <h4>歌词</h4>
                        <div id="lyricInfo" class="lyric-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 获取歌曲详情的函数
        async function fetchSongDetail(songMid, singerMid) {
            try {
                const response = await fetch('/song-detail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `song_mid=${encodeURIComponent(songMid)}&singer_mid=${encodeURIComponent(singerMid)}`
                });
                
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('获取歌曲详情失败:', error);
                return null;
            }
        }
        
        // 显示歌曲详情
        function showSongDetail(detailData) {
            // 显示基本信息
            const basicInfo = document.getElementById('basicInfo');
            if (!detailData || !detailData.song_info || detailData.song_info.ret !== 0 || 
                !detailData.song_info.songlist || detailData.song_info.songlist.length === 0) {
                basicInfo.innerHTML = '<p>获取歌曲基本信息失败</p>';
            } else {
                const song = detailData.song_info.songlist[0];
                let detailHtml = `
                    <div class="detail-row">
                        <span class="detail-label">歌曲名称:</span>
                        <span>${song.song_name || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">歌手:</span>
                        <span>${song.singer_name || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">专辑:</span>
                        <span>${song.album_name || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">播放权限:</span>
                        <span>${song.user_own_rule === 1 ? '可播放' : '仅浏览'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">歌曲类型:</span>
                        <span>${song.song_type || '未知'}</span>
                    </div>
                `;
                
                // 添加播放链接（如果可用）
                // 根据接口文档，song_type为3或13时可以播放
                const canPlay = song.song_type === 3 || song.song_type === 13;
                if (canPlay && song.user_own_rule === 1) {
                    if (song.song_play_url) {
                        detailHtml += `
                            <div class="detail-row">
                                <span class="detail-label">流畅品质:</span>
                                <a href="${song.song_play_url}" class="play-url" target="_blank">播放</a>
                            </div>
                        `;
                    }
                    if (song.song_play_url_standard) {
                        detailHtml += `
                            <div class="detail-row">
                                <span class="detail-label">标准品质:</span>
                                <a href="${song.song_play_url_standard}" class="play-url" target="_blank">播放</a>
                            </div>
                        `;
                    }
                    if (song.song_play_url_hq) {
                        detailHtml += `
                            <div class="detail-row">
                                <span class="detail-label">高品质:</span>
                                <a href="${song.song_play_url_hq}" class="play-url" target="_blank">播放</a>
                            </div>
                        `;
                    }
                    if (song.song_play_url_sq) {
                        detailHtml += `
                            <div class="detail-row">
                                <span class="detail-label">无损品质:</span>
                                <a href="${song.song_play_url_sq}" class="play-url" target="_blank">播放</a>
                            </div>
                        `;
                    }
                } else if (!canPlay) {
                    detailHtml += `
                        <div class="detail-row">
                            <span class="detail-label">播放状态:</span>
                            <span style="color: gray;">该歌曲无法播放（非库内歌曲）</span>
                        </div>
                    `;
                } else {
                    detailHtml += `
                        <div class="detail-row">
                            <span class="detail-label">试听:</span>
                            <span>${song.try_playable === 1 && song.try_30s_url ? 
                                `<a href="${song.try_30s_url}" class="play-url" target="_blank">试听</a>` : 
                                '无试听权限'}</span>
                        </div>
                    `;
                }
                
                basicInfo.innerHTML = detailHtml;
            }
            
            // 显示头像
            const avatarInfo = document.getElementById('avatarInfo');
            if (!detailData || !detailData.avatar || detailData.avatar.ret !== 0) {
                avatarInfo.innerHTML = '<p>获取歌手头像失败</p>';
            } else {
                if (detailData.avatar.singer_avatar) {
                    avatarInfo.innerHTML = `<img src="${detailData.avatar.singer_avatar}" alt="歌手头像" class="avatar-img">`;
                } else {
                    avatarInfo.innerHTML = '<p>暂无头像</p>';
                }
            }
            
            // 显示歌词
            const lyricInfo = document.getElementById('lyricInfo');
            if (!detailData || !detailData.lyric || detailData.lyric.ret !== 0) {
                lyricInfo.innerHTML = '<p>获取歌词失败</p>';
            } else {
                if (detailData.lyric.lyric) {
                    lyricInfo.textContent = detailData.lyric.lyric;
                } else {
                    lyricInfo.innerHTML = '<p>暂无歌词</p>';
                }
            }
        }
    
    // 关闭模态框
    document.querySelector('.close').onclick = function() {
        document.getElementById('songDetailModal').style.display = 'none';
    }
    
    // 点击模态框外部关闭
    window.onclick = function(event) {
        const modal = document.getElementById('songDetailModal');
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    }
    
    // 为所有"详情"按钮添加点击事件
    document.querySelectorAll('.view-details-btn').forEach(button => {
        button.addEventListener('click', async function() {
            const songMid = this.getAttribute('data-songmid');
            const singerMid = this.getAttribute('data-singermid');
            const detailData = await fetchSongDetail(songMid, singerMid);
            showSongDetail(detailData);
            document.getElementById('songDetailModal').style.display = 'block';
        });
    });
    </script>
</body>
</html>