<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ音乐 - 电台专辑列表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
                
        .category-description {
            font-size: 18px;
            color: #666;
            margin-bottom: 5px;
        }
                
        .category-meta {
            font-size: 14px;
            color: #999;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .nav-button.active {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover:not(.active) {
            background-color: #dee2e6;
        }
        .category-info {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .category-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }
        .sort-options {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .sort-option {
            padding: 8px 15px;
            background-color: #e9ecef;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .sort-option.active {
            background-color: #007bff;
            color: white;
        }
        .sort-option:hover:not(.active) {
            background-color: #dee2e6;
        }
        .album-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .album-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            background-color: #fff;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .album-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .album-cover {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .album-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .album-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .album-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 5px;
            margin-top: 10px;
        }
        .album-tag {
            font-size: 12px;
            padding: 2px 6px;
            background-color: #e9ecef;
            border-radius: 3px;
        }
        .vip-tag {
            background-color: #ffc107;
            color: #000;
        }
        
        .album-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .pay-tag {
            background-color: #ff0000;
            color: white;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 10px;
        }
        .page-link {
            padding: 8px 15px;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
            border-radius: 3px;
        }
        .page-link.active {
            background-color: #007bff;
            color: white;
        }
        .page-link:hover:not(.active) {
            background-color: #e9ecef;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .loading, .error {
            text-align: center;
            padding: 40px;
        }
        .loading {
            color: #666;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin: 20px 0;
        }
        .empty-list {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .modal-title {
            font-size: 24px;
            color: #333;
            margin: 0;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .top100-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .top100-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            background-color: #f9f9f9;
        }
        
        .top100-cover {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 3px;
            margin-bottom: 8px;
        }
        
        .top100-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .top100-subtitle {
            font-size: 12px;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .loading-modal {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ音乐 - 电台专辑</h1>
        <a href="/" class="back-link">← 返回首页</a>
        
        <div class="nav-buttons">
            <a href="/search" class="nav-button">音乐搜索</a>
            <a href="/radio" class="nav-button">电台分类</a>
            <a href="/ai-chat" class="nav-button">🤖 AI 对话</a>
            <button class="nav-button" style="background-color: #28a745; color: white;" onclick="downloadTop100Excel()" type="button">
                下载儿童分类Top100 Excel
            </button>
        </div>
        
        <div class="category-info" th:if="${categoryTitle}">
            <div class="category-title" th:text="${categoryTitle}"></div>
        </div>

        <!-- 错误信息显示 -->
        <div class="error" th:if="${error}">
            <p th:text="'错误：' + ${error}"></p>
            <a href="/radio" class="nav-button">返回电台分类</a>
        </div>
        
        <!-- 排序选项 -->
        <div th:if="${error == null}" class="sort-options">
            <div class="sort-option" 
                 th:classappend="${sortType == 0 ? 'active' : ''}"
                 data-sort="0"
                 onclick="changeSort(0)">最新</div>
            
            <div class="sort-option" 
                 th:classappend="${sortType == 1 ? 'active' : ''}"
                 data-sort="1"
                 onclick="changeSort(1)">最热</div>
        </div>
        
        <!-- 专辑列表 -->
        <div class="album-list" th:if="${albumLists != null and error == null}">
            <div class="album-item" th:each="album : ${albumLists}">
                <a th:href="@{/radio/album/{albumId}(albumId=${album.get('albumId')}, albumMid=${album.get('album_mid')})}" class="album-link">
                    <img th:src="${album.get('cover') != null ? album.get('cover') : 'https://y.gtimg.cn/mediastyle/global/img/album_300.png'}" 
                         alt="专辑封面" 
                         class="album-cover"
                         onerror="this.src='https://y.gtimg.cn/mediastyle/global/img/album_300.png'">
                    <div class="album-title" th:text="${album.get('title') != null ? album.get('title') : '未知专辑'}"></div>
                    <div class="album-subtitle" th:text="${album.get('subTitle') != null ? album.get('subTitle') : '暂无简介'}"></div>
                    <div class="album-tags">
                        <div th:if="${album.get('lr_pay_type') != null and album.get('lr_pay_type') == 1}" class="album-tag pay-tag">VIP免费</div>
                        <div th:if="${album.get('should_pay') != null and album.get('should_pay') == 1 and (album.get('lr_pay_type') == null or album.get('lr_pay_type') != 1)}" class="album-tag pay-tag">付费</div>
                        <div th:if="${album.get('listen_cnt') != null}" class="album-tag">播放量: <span class="play-count-formatted" th:attr="data-count=${album.get('listen_cnt')}" th:text="${#numbers.formatDecimal(album.get('listen_cnt'), 1, 'COMMA', 0, 'POINT')}"></span></div>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- 空列表提示 -->
        <div class="empty-list" th:if="${albumLists != null and albumLists.empty and error == null}">
            <p>该分类下暂无专辑</p>
        </div>
        
        <!-- 加载中提示 -->
        <div class="loading" th:if="${albumLists == null and error == null}">
            <p>加载中...</p>
        </div>
        
        <div th:if="${total != null and total > 0 and error == null}" class="pagination">
            <a href="#" class="page-link" th:if="${currentPage > 0}" th:onclick="'changePage(' + ${currentPage - 1} + ')'">上一页</a>
            <span class="page-link active" th:text="${currentPage + 1}"></span>
            <a href="#" class="page-link" th:if="${(currentPage + 1) * pageSize < total}" th:onclick="'changePage(' + ${currentPage + 1} + ')'">下一页</a>
        </div>
    </div>

    <!-- Top100弹窗 -->
    <div id="top100Modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">最热Top100歌单</h2>
                <span class="close" onclick="closeTop100Modal()">&times;</span>
            </div>
            <div id="top100Content">
                <div class="loading-modal">加载中...</div>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        /*<![CDATA[*/
        const fstId = /*[[${fstId}]]*/ 0;
        const sndId = /*[[${sndId}]]*/ 0;
        let currentSortType = /*[[${sortType}]]*/ 1;
        const currentPage = /*[[${currentPage}]]*/ 0;
        const pageSize = /*[[${pageSize}]]*/ 20;
        const categoryTitle = /*[[${categoryTitle}]]*/ '';
        
        function buildUrl(baseParams) {
            let url = `/radio/albums?fstId=${fstId}&sndId=${sndId}&title=${encodeURIComponent(categoryTitle)}`;
            
            // 添加基础参数
            for (let key in baseParams) {
                if (baseParams[key] !== null && baseParams[key] !== undefined && baseParams[key] !== '') {
                    url += `&${key}=${baseParams[key]}`;
                }
            }
            
            return url;
        }
        
        function changeSort(sortType) {
            currentSortType = sortType;
            window.location.href = buildUrl({sortType: sortType, page: 0});
        }
        
        function changePage(page) {
            window.location.href = buildUrl({sortType: currentSortType, page: page});
        }

        // 格式化大数字显示（播放量）
        function formatPlayCount(count) {
            if (!count || count === 0) return '0';
            
            if (count >= 100000000) {
                // 亿级别
                return (count / 100000000).toFixed(1) + '亿';
            } else if (count >= 10000) {
                // 万级别
                return (count / 10000).toFixed(1) + '万';
            } else {
                // 普通数字，添加千位分隔符
                return count.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }
        }

        // 页面加载完成后格式化所有播放量
        document.addEventListener('DOMContentLoaded', function() {
            const playCountElements = document.querySelectorAll('.play-count-formatted');
            playCountElements.forEach(function(element) {
                const count = parseInt(element.getAttribute('data-count'));
                if (count) {
                    element.textContent = formatPlayCount(count);
                }
            });
        });
        
        // 下载儿童分类(fstId=1153)下所有子分类的Top100歌单Excel
        async function downloadTop100Excel() {
            console.log('点击了下载Excel按钮');
            const modal = document.getElementById('top100Modal');
            const content = document.getElementById('top100Content');
            
            // 显示弹窗和加载状态，添加进度条
            modal.style.display = 'block';
            content.innerHTML = `
                <div class="loading-modal">
                    正在获取儿童分类下所有子分类的Top100歌单...<br>
                    <div style="margin: 20px 0;">
                        <div style="background-color: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                            <div id="progressBar" style="background-color: #007bff; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <div id="progressText" style="margin-top: 10px; font-size: 14px;">0%</div>
                    </div>
                    <span id="loadingProgress">准备中...</span>
                </div>
            `;
            
            try {
                // 首先获取电台分类页面，找到儿童分类(fstId=1153)下的所有子分类
                console.log('开始获取儿童分类信息...');
                const categoriesResponse = await fetch('/radio', {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                if (!categoriesResponse.ok) {
                    throw new Error('获取分类信息失败');
                }
                
                const categoriesHtml = await categoriesResponse.text();
                const parser = new DOMParser();
                const categoriesDoc = parser.parseFromString(categoriesHtml, 'text/html');
                
                // 查找儿童分类(fstId=1153)下的所有子分类
                const childrenSubcategories = [];
                const categoryItems = categoriesDoc.querySelectorAll('.category-item');
                
                categoryItems.forEach(categoryItem => {
                    const subcategoryItems = categoryItem.querySelectorAll('.subcategory-item');
                    subcategoryItems.forEach(subcategoryItem => {
                        const fstIdAttr = subcategoryItem.getAttribute('data-fst-id');
                        const sndIdAttr = subcategoryItem.getAttribute('data-snd-id');
                        const titleText = subcategoryItem.textContent.trim();
                        
                        // 只获取儿童分类(fstId=1153)下的子分类
                        if (fstIdAttr && sndIdAttr && titleText && parseInt(fstIdAttr) === 1153) {
                            childrenSubcategories.push({
                                fstId: parseInt(fstIdAttr),
                                sndId: parseInt(sndIdAttr),
                                title: titleText
                            });
                        }
                    });
                });
                
                console.log('找到儿童分类下子分类数量:', childrenSubcategories.length);
                console.log('儿童子分类列表:', childrenSubcategories);
                
                if (childrenSubcategories.length === 0) {
                    content.innerHTML = '<div class="loading-modal">未找到儿童分类下的子分类信息</div>';
                    return;
                }
                
                const allAlbums = [];
                
                // 计算总的步骤数（每个子分类最多5页）
                const totalSteps = childrenSubcategories.length * 5;
                let completedSteps = 0;
                
                // 更新进度条的函数
                function updateProgress(current, total, message) {
                    const percentage = Math.round((current / total) * 100);
                    console.log(`更新进度: ${current}/${total} = ${percentage}%`);
                    
                    const progressBar = document.getElementById('progressBar');
                    const progressText = document.getElementById('progressText');
                    const progressElement = document.getElementById('loadingProgress');
                    
                    if (progressBar) {
                        progressBar.style.width = percentage + '%';
                        console.log('进度条宽度已更新:', percentage + '%');
                    }
                    if (progressText) {
                        progressText.textContent = percentage + '%';
                    }
                    if (progressElement) {
                        progressElement.innerHTML = message;
                    }
                }
                
                // 初始化进度
                updateProgress(0, totalSteps, '开始获取儿童分类数据...');
                
                // 遍历儿童分类下的每个子分类，获取其Top100
                for (let i = 0; i < childrenSubcategories.length; i++) {
                    const subcategory = childrenSubcategories[i];
                    
                    updateProgress(completedSteps, totalSteps, `正在处理儿童子分类: ${subcategory.title}<br>进度: ${i + 1}/${childrenSubcategories.length}<br>已获取歌单: ${allAlbums.length}`);
                    
                    console.log(`处理儿童子分类 ${i + 1}/${childrenSubcategories.length}: ${subcategory.title} (fstId=${subcategory.fstId}, sndId=${subcategory.sndId})`);
                    
                    // 为每个子分类获取前5页数据（100个）
                    const pageSize = 20;
                    const maxPages = 5;
                    
                    for (let page = 0; page < maxPages; page++) {
                        try {
                            // 显示等待状态
                            updateProgress(completedSteps, totalSteps, `等待中... 即将爬取 ${subcategory.title} (第${page + 1}页)<br>进度: ${i + 1}/${childrenSubcategories.length}<br>已获取歌单: ${allAlbums.length}`);
                            
                            // 添加400ms延迟
                            await new Promise(resolve => setTimeout(resolve, 400));
                            
                            // 显示爬取状态
                            updateProgress(completedSteps, totalSteps, `正在爬取 ${subcategory.title} (第${page + 1}页)...<br>进度: ${i + 1}/${childrenSubcategories.length}<br>已获取歌单: ${allAlbums.length}`);
                            
                            const url = `/radio/albums?fstId=${subcategory.fstId}&sndId=${subcategory.sndId}&title=${encodeURIComponent(subcategory.title)}&sortType=1&page=${page}&pageSize=${pageSize}`;
                            
                            console.log(`请求儿童子分类 ${subcategory.title} 第${page + 1}页:`, url);
                            
                            const response = await fetch(url, {
                                method: 'GET',
                                credentials: 'same-origin'
                            });
                            
                            if (!response.ok) {
                                console.warn(`儿童子分类 ${subcategory.title} 第${page + 1}页请求失败:`, response.status);
                                break;
                            }
                            
                            const html = await response.text();
                            const doc = parser.parseFromString(html, 'text/html');
                            const albumItems = doc.querySelectorAll('.album-item');
                            
                            console.log(`儿童子分类 ${subcategory.title} 第${page + 1}页找到歌单数量:`, albumItems.length);
                            
                            if (albumItems.length === 0) {
                                console.log(`儿童子分类 ${subcategory.title} 第${page + 1}页无数据，停止该分类请求`);
                                break;
                            }
                            
                            // 提取歌单数据
                            albumItems.forEach((item, index) => {
                                const link = item.querySelector('.album-link');
                                const cover = item.querySelector('.album-cover');
                                const title = item.querySelector('.album-title');
                                const subtitle = item.querySelector('.album-subtitle');
                                const playCountElement = item.querySelector('.play-count-formatted');
                                
                                const albumData = {
                                    href: link ? link.getAttribute('href') : '#',
                                    coverSrc: cover ? cover.getAttribute('src') : 'https://y.gtimg.cn/mediastyle/global/img/album_300.png',
                                    titleText: title ? title.textContent.trim() : '未知专辑',
                                    subtitleText: subtitle ? subtitle.textContent.trim() : '暂无简介',
                                    playCount: playCountElement ? playCountElement.textContent : '',
                                    categoryTitle: subcategory.title,
                                    fstId: subcategory.fstId,
                                    sndId: subcategory.sndId,
                                    globalRank: allAlbums.length + 1
                                };
                                
                                allAlbums.push(albumData);
                            });
                            
                            // 更新进度
                            completedSteps++;
                            updateProgress(completedSteps, totalSteps, `完成爬取 ${subcategory.title} (第${page + 1}页)<br>进度: ${i + 1}/${childrenSubcategories.length}<br>已获取歌单: ${allAlbums.length}`);
                            
                        } catch (error) {
                            console.error(`儿童子分类 ${subcategory.title} 第${page + 1}页请求出错:`, error);
                            completedSteps++;
                            break;
                        }
                    }
                    
                    // 子分类间添加稍长延迟
                    updateProgress(completedSteps, totalSteps, `等待中... 准备处理下一个子分类<br>进度: ${i + 1}/${childrenSubcategories.length}<br>已获取歌单: ${allAlbums.length}`);
                    await new Promise(resolve => setTimeout(resolve, 400));
                }
                
                console.log('所有儿童子分类处理完成');
                
                if (allAlbums.length === 0) {
                    content.innerHTML = '<div class="loading-modal">儿童分类下所有子分类都暂无歌单数据</div>';
                    return;
                }
                
                // 解析 albumId 辅助函数
                function extractAlbumIdFromHref(href) {
                    if (!href) return '';
                    const match = href.match(/\/radio\/album\/(\d+)/);
                    return match ? match[1] : '';
                }

                // 批量获取歌单详情（带进度条）
                async function fetchAlbumDetails(albums) {
                    const detailMap = {};
                    let finished = 0;
                    // 初始化进度条
                    updateProgress(0, albums.length, `正在获取歌单详情（0/${albums.length}）`);
                    for (let i = 0; i < albums.length; i++) {
                        const album = albums[i];
                        const albumId = extractAlbumIdFromHref(album.href);
                        album.albumId = albumId;
                        let albumName = album.titleText || '';
                        if (!albumId) {
                            finished++;
                            updateProgress(finished, albums.length, `正在获取歌单详情（${finished}/${albums.length}）：${albumName}`);
                            continue;
                        }
                        try {
                            updateProgress(finished + 1, albums.length, `正在获取歌单详情（${finished + 1}/${albums.length}）：${albumName}`);
                            const resp = await fetch(`/api/radio/album/${albumId}`);
                            if (resp.ok) {
                                const data = await resp.json();
                                detailMap[albumId] = data;
                            }
                        } catch (e) {
                            // 忽略单个失败
                        }
                        finished++;
                        updateProgress(finished, albums.length, `正在获取歌单详情（${finished}/${albums.length}）：${albumName}`);
                    }
                    return detailMap;
                }

                // 按分类分组数据，保持接口返回的最热顺序
                const albumsByCategory = {};
                allAlbums.forEach(album => {
                    if (!albumsByCategory[album.categoryTitle]) {
                        albumsByCategory[album.categoryTitle] = [];
                    }
                    albumsByCategory[album.categoryTitle].push(album);
                });

                // 拉取所有歌单详情，进度条提示
                updateProgress(0, allAlbums.length, `正在批量获取歌单详情 0/${allAlbums.length}`);
                const albumDetails = await fetchAlbumDetails(allAlbums);

                // 歌单详情全部获取完成，提示生成Excel
                updateProgress(allAlbums.length, allAlbums.length, '歌单详情获取完成，正在生成Excel...');

                // 组装Excel数据
                const excelData = [];
                childrenSubcategories.forEach(subcategory => {
                    const categoryAlbums = albumsByCategory[subcategory.title] || [];
                    categoryAlbums.slice(0, 100).forEach((album, index) => {
                        const detail = albumDetails[album.albumId] || {};
                        // 每一集单独一行
                        if (detail.songNames && Array.isArray(detail.songNames) && detail.songNames.length > 0) {
                            detail.songNames.forEach((epName, epIdx) => {
                                excelData.push({
                                    '分类名称': subcategory.title,
                                    '排名': index + 1,
                                    '歌单名称': album.titleText,
                                    '作者/简介': album.subtitleText,
                                    '播放量': album.playCount || '暂无数据',
                                    '发布时间': '暂无数据',
                                    '歌单id': album.albumId || '',
                                    '歌单头像': detail.cover || album.coverSrc || '',
                                    '歌单歌曲数': detail.songCount || '',
                                    '每一集的名称': epName
                                });
                            });
                        } else {
                            excelData.push({
                                '分类名称': subcategory.title,
                                '排名': index + 1,
                                '歌单名称': album.titleText,
                                '作者/简介': album.subtitleText,
                                '播放量': album.playCount || '暂无数据',
                                '发布时间': '暂无数据',
                                '歌单id': album.albumId || '',
                                '歌单头像': detail.cover || album.coverSrc || '',
                                '歌单歌曲数': detail.songCount || '',
                                '每一集的名称': ''
                            });
                        }
                    });
                });

                console.log('准备生成Excel，数据条数:', excelData.length);

                // 生成Excel文件
                generateAndDownloadExcel(excelData, '儿童分类Top100歌单汇总');
                
                // 关闭弹窗
                closeTop100Modal();
                
            } catch (error) {
                console.error('获取儿童分类Top100失败:', error);
                content.innerHTML = `<div class="loading-modal" style="color: #dc3545;">
                    获取数据失败: ${error.message}<br>
                    <small>请确保已登录并选择了有效的分类</small>
                </div>`;
            }
        }
        
        // 关闭Top100弹窗
        function closeTop100Modal() {
            const modal = document.getElementById('top100Modal');
            modal.style.display = 'none';
        }
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('top100Modal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
        
        // 生成并下载Excel文件
        function generateAndDownloadExcel(data, filename) {
            console.log('开始生成Excel文件，数据条数:', data.length);
            
            try {
                // 创建CSV内容（简单的Excel替代方案）
                let csvContent = '\uFEFF'; // BOM for UTF-8
                
                // 添加表头
                const headers = [
                    '分类名称', '排名', '歌单名称', '作者/简介', '播放量', '发布时间',
                    '歌单id', '歌单头像', '歌单歌曲数', '每一集的名称'
                ];
                csvContent += headers.join(',') + '\n';
                
                // 添加数据行
                data.forEach(row => {
                    const values = headers.map(header => {
                        let value = row[header] || '';
                        // 处理包含逗号或引号的值
                        if (value.toString().includes(',') || value.toString().includes('"') || value.toString().includes('\n')) {
                            value = '"' + value.toString().replace(/"/g, '""') + '"';
                        }
                        return value;
                    });
                    csvContent += values.join(',') + '\n';
                });
                
                // 创建Blob对象
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                
                // 创建下载链接
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename + '.csv');
                link.style.visibility = 'hidden';
                
                // 添加到页面并触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 清理URL对象
                URL.revokeObjectURL(url);
                
                console.log('Excel文件下载完成:', filename + '.csv');
                alert('Excel文件已生成并下载完成！\n文件名: ' + filename + '.csv\n数据条数: ' + data.length);
                
            } catch (error) {
                console.error('生成Excel文件失败:', error);
                alert('生成Excel文件失败: ' + error.message);
            }
        }
        
        /*]]>*/
    </script>
</body>
</html>