<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨星儿童教育助手 - AI 对话</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --mac-primary: #007AFF;
            --mac-secondary: #5AC8FA;
            --mac-success: #34C759;
            --mac-warning: #FF9500;
            --mac-danger: #FF3B30;
            --mac-dark: #1C1C1E;
            --mac-gray: #8E8E93;
            --mac-light-gray: #F2F2F7;
            --mac-border: #E5E5EA;
            --mac-shadow: rgba(0, 0, 0, 0.1);
            --mac-text: #000000;
            --mac-text-secondary: #3C3C43;
            --mac-bg: #FFFFFF;
            --mac-blur: rgba(255, 255, 255, 0.72);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--mac-text);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--mac-bg);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
            border-radius: 0;
            overflow: hidden;
        }

        .header {
            background: var(--mac-bg);
            border-bottom: 1px solid var(--mac-border);
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            background-color: var(--mac-blur);
            z-index: 10;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 14px;
        }

        .header-title h1 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--mac-text);
            letter-spacing: -0.01em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .header-title-icon {
            background: linear-gradient(135deg, var(--mac-primary) 0%, var(--mac-secondary) 100%);
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .header-title-icon:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 122, 255, 0.3);
        }

        .nav-back {
            background: rgba(0, 0, 0, 0.04);
            border: none;
            font-size: 15px;
            color: var(--mac-primary);
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-back::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(0, 122, 255, 0.1) 0%, rgba(0, 122, 255, 0) 70%);
            transform: scale(0);
            transition: transform 0.5s ease-out;
        }

        .nav-back:hover::before {
            transform: scale(2);
        }

        .nav-back:hover {
            background: rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .nav-back:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: var(--mac-bg);
        }

        .messages-area {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            display: flex;
            flex-direction: column;
            gap: 20px;
            background: linear-gradient(to bottom, var(--mac-bg), var(--mac-light-gray));
        }

        .message {
            display: flex;
            gap: 16px;
            max-width: 80%;
            animation: messageSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes messageSlideIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Mac风格悬停效果 */
        .mac-hover-lift {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .mac-hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Mac风格按钮点击效果 */
        .mac-button-press {
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .mac-button-press:active {
            transform: scale(0.96);
        }

        /* Mac风格淡入效果 */
        .mac-fade-in {
            animation: macFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        }

        @keyframes macFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Mac风格滑入效果 */
        .mac-slide-in {
            animation: macSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        }

        @keyframes macSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Mac风格缩放效果 */
        .mac-scale-in {
            animation: macScaleIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
        }

        @keyframes macScaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 应用Mac风格动画到元素 */
        .message-avatar {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .message:hover .message-avatar {
            transform: scale(1.1);
        }

        .send-button, .upload-button {
            position: relative;
            overflow: hidden;
        }

        .send-button::after, .upload-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .send-button:active::after, .upload-button:active::after {
            width: 300px;
            height: 300px;
        }

        /* 消息内容悬停效果 */
        .message-content {
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .message:hover .message-content {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        /* 滚动条美化 */
        .messages-area::-webkit-scrollbar {
            width: 8px;
        }

        .messages-area::-webkit-scrollbar-track {
            background: var(--mac-light-gray);
            border-radius: 4px;
        }

        .messages-area::-webkit-scrollbar-thumb {
            background: var(--mac-gray);
            border-radius: 4px;
            transition: background 0.3s;
        }

        .messages-area::-webkit-scrollbar-thumb:hover {
            background: var(--mac-text-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                border-radius: 0;
                box-shadow: none;
            }

            .header {
                padding: 12px 16px;
            }

            .header-title h1 {
                font-size: 1.1rem;
            }

            .header-title-icon {
                width: 36px;
                height: 36px;
                font-size: 18px;
            }

            .nav-back {
                padding: 6px 12px;
                font-size: 14px;
            }

            .messages-area {
                padding: 16px;
                gap: 16px;
            }

            .message {
                max-width: 90%;
            }

            .message-content {
                font-size: 15px;
                padding: 12px 16px;
            }

            .input-area {
                padding: 16px;
                gap: 12px;
            }

            .message-input {
                font-size: 15px;
                padding: 12px 16px;
            }

            .send-button, .upload-button {
                height: 46px;
                padding: 0 18px;
                font-size: 15px;
            }

            .file-name {
                font-size: 13px;
                padding: 10px 12px;
            }
        }

        @media (max-width: 480px) {
            .header-title h1 {
                font-size: 1rem;
            }

            .nav-back span {
                display: none;
            }

            .nav-back {
                padding: 8px;
                width: 36px;
                height: 36px;
                justify-content: center;
            }

            .message {
                max-width: 95%;
                gap: 12px;
            }

            .message-avatar {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .message-content {
                font-size: 14px;
                padding: 10px 14px;
            }

            .input-area {
                flex-wrap: wrap;
            }

            .send-button {
                width: 100%;
                margin-top: 8px;
            }

            .upload-button {
                padding: 0 12px;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --mac-primary: #0A84FF;
                --mac-secondary: #409CFF;
                --mac-success: #32D74B;
                --mac-warning: #FF9F0A;
                --mac-danger: #FF453A;
                --mac-dark: #000000;
                --mac-gray: #98989D;
                --mac-light-gray: #1C1C1E;
                --mac-border: #38383A;
                --mac-shadow: rgba(0, 0, 0, 0.3);
                --mac-text: #FFFFFF;
                --mac-text-secondary: #EBEBF5;
                --mac-bg: #000000;
                --mac-blur: rgba(30, 30, 30, 0.72);
            }

            body {
                background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
            }

            .messages-area {
                background: linear-gradient(to bottom, var(--mac-bg), #1c1c1e);
            }

            .message.assistant .message-content {
                background: #1c1c1e;
                border-color: #38383A;
            }

            .message.assistant .message-content::after {
                border-color: transparent #1c1c1e transparent transparent;
            }

            .message-input {
                background: rgba(30, 30, 30, 0.8);
                border-color: #38383A;
                color: var(--mac-text);
            }

            .message-input:focus {
                background: rgba(30, 30, 30, 0.95);
            }

            .upload-button {
                background: rgba(255, 255, 255, 0.1);
                border-color: #38383A;
                color: var(--mac-primary);
            }

            .upload-button:hover {
                background: rgba(255, 255, 255, 0.15);
                border-color: var(--mac-gray);
            }

            .file-name {
                background: #1c1c1e;
                border-color: #38383A;
            }

            .messages-area::-webkit-scrollbar-track {
                background: #1c1c1e;
            }

            .messages-area::-webkit-scrollbar-thumb {
                background: #38383A;
            }

            .messages-area::-webkit-scrollbar-thumb:hover {
                background: #48484A;
            }
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message.assistant {
            align-self: flex-start;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, var(--mac-primary) 0%, var(--mac-secondary) 100%);
        }

        .message.assistant .message-avatar {
            background: linear-gradient(135deg, var(--mac-success) 0%, #30A14E 100%);
        }

        .message-content {
            max-width: 70%;
            padding: 14px 18px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.6;
            font-size: 16px;
            position: relative;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            background: var(--mac-bg);
            margin-bottom: 4px;
            margin-top: 4px;
            border: 1px solid var(--mac-border);
            /* markdown适配增强 */
        }
        .message-content p {
            margin: 0 0 1em 0;
            line-height: 1.8;
            word-break: break-word;
        }
        .message-content ul, .message-content ol {
            margin: 0 0 1em 2em;
            padding-left: 1.5em;
        }
        .message-content ul {
            list-style: disc inside;
        }
        .message-content ol {
            list-style: decimal inside;
        }
        .message-content li {
            margin-bottom: 0.4em;
            line-height: 1.7;
        }
        .message-content pre {
            background: #23272e;
            color: #f8f8f2;
            border-radius: 8px;
            padding: 14px 16px;
            margin: 1.2em 0;
            font-size: 15px;
            overflow-x: auto;
            font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
            position: relative;
        }
        .message-content pre code {
            background: none;
            color: inherit;
            padding: 0;
            font-size: 15px;
        }
        .message-content code {
            background: #f4f4f4;
            color: #c7254e;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 15px;
            margin: 0 2px;
            font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
        }
        .message-content blockquote {
            border-left: 4px solid #a0aec0;
            background: #f7fafc;
            color: #555;
            margin: 1.2em 0;
            padding: 10px 18px;
            border-radius: 6px;
            font-style: italic;
            font-size: 15px;
        }
        .message-content h1,
        .message-content h2,
        .message-content h3,
        .message-content h4,
        .message-content h5,
        .message-content h6 {
            margin: 1.4em 0 0.7em 0;
            font-weight: bold;
            line-height: 1.3;
            color: #2d3748;
            word-break: break-word;
        }
        .message-content h1 { font-size: 1.6em; border-bottom: 2px solid #e2e8f0; padding-bottom: 0.2em;}
        .message-content h2 { font-size: 1.35em; border-bottom: 1px solid #e2e8f0; padding-bottom: 0.15em;}
        .message-content h3 { font-size: 1.15em; }
        .message-content h4 { font-size: 1.08em; }
        .message-content h5 { font-size: 1em; }
        .message-content h6 { font-size: 0.95em; }
        .message-content hr {
            border: none;
            border-top: 1px solid #e2e8f0;
            margin: 1.2em 0;
        }
        .message-content table {
            border-collapse: collapse;
            margin: 1em 0;
            width: 100%;
            background: #fff;
            font-size: 15px;
        }
        .message-content th,
        .message-content td {
            border: 1px solid #e2e8f0;
            padding: 7px 14px;
            text-align: left;
        }
        .message-content tr:nth-child(even) {
            background: #f7fafc;
        }
        .message-content img {
            max-width: 100%;
            border-radius: 6px;
            margin: 0.7em auto;
            display: block;
        }
        .message-content a {
            color: #3182ce;
            text-decoration: underline;
            word-break: break-all;
            transition: color 0.2s;
        }
        .message-content a:hover {
            color: #2b6cb0;
            text-decoration: underline wavy;
        }
        .message-content kbd {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 0.95em;
            font-family: 'JetBrains Mono', 'Fira Mono', 'Consolas', 'Menlo', monospace;
            color: #333;
            margin: 0 2px;
        }
        
        /* 用户气泡 */
        .message.user .message-content {
            background: linear-gradient(135deg, var(--mac-primary) 0%, var(--mac-secondary) 100%);
            color: #fff;
            border-bottom-right-radius: 6px;
            border-bottom-left-radius: 20px;
            border-top-right-radius: 20px;
            border-top-left-radius: 20px;
            border: none;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
            animation: messagePopIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        .message.user .message-content::after {
            content: "";
            position: absolute;
            right: -10px;
            top: 20px;
            border-width: 8px 0 8px 10px;
            border-style: solid;
            border-color: transparent transparent transparent var(--mac-primary);
            filter: drop-shadow(0 2px 4px rgba(0, 122, 255, 0.2));
        }
        
        /* AI气泡 */
        .message.assistant .message-content {
            background: var(--mac-bg);
            color: var(--mac-text);
            border-bottom-left-radius: 6px;
            border-bottom-right-radius: 20px;
            border-top-right-radius: 20px;
            border-top-left-radius: 20px;
            border: 1px solid var(--mac-border);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            animation: messagePopIn 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        .message.assistant .message-content::after {
            content: "";
            position: absolute;
            left: -10px;
            top: 20px;
            border-width: 8px 10px 8px 0;
            border-style: solid;
            border-color: transparent var(--mac-bg) transparent transparent;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.05));
        }

        @keyframes messagePopIn {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); opacity: 1; }
        }

        .input-area {
            padding: 20px 24px;
            background: var(--mac-bg);
            border-top: 1px solid var(--mac-border);
            display: flex;
            gap: 16px;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            background-color: var(--mac-blur);
        }

        .message-input {
            flex: 1;
            border: 1px solid var(--mac-border);
            border-radius: 12px;
            padding: 14px 18px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            min-height: 20px;
            max-height: 200px;
            align-self: center;
            background: rgba(255, 255, 255, 0.8);
            font-family: inherit;
        }

        .message-input:focus {
            border-color: var(--mac-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.15);
            background: rgba(255, 255, 255, 0.95);
        }

        .message-input::placeholder {
            color: var(--mac-gray);
        }

        .send-button {
            background: linear-gradient(135deg, var(--mac-primary) 0%, var(--mac-secondary) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0 22px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            align-self: center;
            height: 50px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 122, 255, 0.3);
        }

        .send-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .upload-button {
            background: rgba(0, 0, 0, 0.04);
            color: var(--mac-primary);
            border: 1px solid var(--mac-border);
            border-radius: 12px;
            padding: 0 18px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            align-self: center;
            height: 50px;
            font-weight: 500;
        }

        .upload-button:hover {
            background: rgba(0, 0, 0, 0.08);
            border-color: var(--mac-gray);
            transform: translateY(-1px);
        }

        .upload-button:active {
            transform: translateY(0);
        }

        .file-input {
            display: none;
        }

        .file-name {
            font-size: 14px;
            color: var(--mac-text-secondary);
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            grid-column: 1 / 3;
            margin-top: 0;
            padding: 12px 16px;
            background: var(--mac-light-gray);
            border-radius: 12px;
            align-self: center;
            border: 1px solid var(--mac-border);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .file-name::before {
            content: "\f15c";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: var(--mac-primary);
            font-size: 16px;
        }

        .upload-button {
            position: relative;
            overflow: hidden;
        }

        .upload-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(0, 122, 255, 0.1);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .upload-button:active::after {
            width: 300px;
            height: 300px;
        }

        /* 消息气泡动画 */
        
        /* 加载动画 */
        .loading-dots {
            display: inline-block;
            width: 20px;
            text-align: left;
        }
        
        .loading-dots::after {
            content: "";
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { content: ""; }
            25% { content: "."; }
            50% { content: ".."; }
            75% { content: "..."; }
            100% { content: ""; }
        }
        
        /* AI思考加载动画 */
        .ai-thinking-loader {
            border: 2px solid var(--mac-border);
            border-radius: 50%;
            border-top: 2px solid var(--mac-primary);
            width: 24px;
            height: 24px;
            animation: spin 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mac风格加载动画 */
        .mac-loader {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .mac-loader-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--mac-primary);
            animation: macLoaderPulse 1.4s ease-in-out infinite both;
        }

        .mac-loader-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .mac-loader-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes macLoaderPulse {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Mac风格错误提示 */
        .mac-error-message {
            background: rgba(255, 59, 48, 0.1);
            border-left: 4px solid var(--mac-danger);
            color: var(--mac-danger);
            padding: 12px 16px;
            border-radius: 8px;
            margin: 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }

        .mac-error-message::before {
            content: "\f06a";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: var(--mac-danger);
        }

        @keyframes shake {
            10%, 90% {
                transform: translate3d(-1px, 0, 0);
            }
            20%, 80% {
                transform: translate3d(2px, 0, 0);
            }
            30%, 50%, 70% {
                transform: translate3d(-4px, 0, 0);
            }
            40%, 60% {
                transform: translate3d(4px, 0, 0);
            }
        }

        /* Mac风格成功提示 */
        .mac-success-message {
            background: rgba(52, 199, 89, 0.1);
            border-left: 4px solid var(--mac-success);
            color: var(--mac-success);
            padding: 12px 16px;
            border-radius: 8px;
            margin: 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: fadeInUp 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
        }

        .mac-success-message::before {
            content: "\f00c";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: var(--mac-success);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    <!-- markdown-it 及插件 -->
    <script src="https://unpkg.com/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
    <!-- markdown-it 插件暂不引入，确保主库可用 -->
    <script src="https://cdn.jsdelivr.net/npm/event-source-polyfill@1.0.26/src/eventsource.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
</head>
<body>
<div class="app-container">
    <div class="header">
        <div class="header-title">
            <div class="header-title-icon">
                <i class="fa-solid fa-robot"></i>
            </div>
            <h1>晨星儿童教育助手</h1>
        </div>
        <button class="nav-back" onclick="window.location.href='/'">
            <i class="fa-solid fa-arrow-left"></i> 返回首页
        </button>
    </div>
    <div class="chat-container">
        <div class="messages-area" id="messages-area">
            <!-- 消息气泡将在这里动态添加 -->
        </div>

        <form class="input-area" id="chat-form">
            <input type="file" id="file-input" class="file-input" accept=".xls,.xlsx,.doc,.docx,.txt,.pdf" onchange="handleFileSelect(event)">
            <div style="flex: 1; display: grid; grid-template-columns: auto 1fr auto; grid-gap: 14px; align-items: center;">
                <button type="button" class="upload-button" onclick="document.getElementById('file-input').click()" id="upload-btn">
                    <i class="fas fa-paperclip"></i>
                </button>
                <textarea
                    class="message-input"
                    id="user-input"
                    placeholder="输入消息... (Shift+Enter 换行)"
                    rows="1"></textarea>
                <button type="button" class="upload-button" onclick="uploadFile()" id="upload-submit-btn" style="display: none;">
                    <i class="fas fa-upload"></i> 上传
                </button>
                <div id="file-name" class="file-name" style="display: none; grid-column: 1 / 4;"></div>
            </div>
            <button type="submit" class="send-button">
                <i class="fas fa-paper-plane"></i> 发送
            </button>
        </form>
    </div>
</div>
<script>
// 全局JS报错捕获
window.onerror = function(msg, url, line, col, error) {
    window.console.error('全局JS错误:', msg, url, line, col, error);
    alert('前端JS报错：' + msg + ' @' + url + ':' + line);
};
// 消息渲染
function renderMessage(role, content, isStreaming) {
    const area = document.getElementById('messages-area');
    const msgDiv = document.createElement('div');
    msgDiv.className = 'message ' + (role === 'user' ? 'user' : 'assistant');
    msgDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fa-solid ${role === 'user' ? 'fa-user' : 'fa-robot'}"></i>
        </div>
        <div class="message-content">${content}</div>
    `;
    area.appendChild(msgDiv);
    area.scrollTop = area.scrollHeight;
    return msgDiv.querySelector('.message-content');
}

// 代码高亮和公式渲染
const md = window.markdownit({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    highlight: function (str, lang) {
        if (window.hljs && lang && hljs.getLanguage(lang)) {
            try {
                return '<pre class="hljs"><code>' +
                    hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                    '</code></pre>';
            } catch (__) {}
        }
        return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>';
    }
});

function enhanceContent(el) {
    if (!el) return;
    // markdown-it 渲染
    el.innerHTML = md.render(el.innerHTML);
    el.querySelectorAll('pre code').forEach(block => {
        hljs.highlightElement(block);
    });
    renderMathInElement(el, { delimiters: [ {left: "$$", right: "$$", display: true}, {left: "$", right: "$", display: false} ] });
}

// 文件上传相关函数
let uploadedFile = null;

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        uploadedFile = file;
        document.getElementById('file-name').textContent = `已选择: ${file.name} (${(file.size/1024).toFixed(1)}KB)`;
        document.getElementById('file-name').style.display = 'block';
        document.getElementById('upload-submit-btn').style.display = 'flex';
    } else {
        uploadedFile = null;
        document.getElementById('file-name').style.display = 'none';
        document.getElementById('upload-submit-btn').style.display = 'none';
    }
}

async function uploadFile() {
    if (!uploadedFile) {
        alert('请先选择文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', uploadedFile);

    // 显示上传状态
    const uploadBtn = document.getElementById('upload-submit-btn');
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
    uploadBtn.disabled = true;

    // 渲染用户上传提示
    const userMsg = renderMessage('user', `已上传文件: ${uploadedFile.name}`);
    enhanceContent(userMsg);

    // 清理历史AI消息，确保状态干净
    const messagesArea = document.getElementById('messages-area');
    // 保留用户消息，清除所有AI消息
    const userMessages = messagesArea.querySelectorAll('.message.user');
    messagesArea.innerHTML = '';
    userMessages.forEach(msg => messagesArea.appendChild(msg));

    try {
        // 直接使用fetch进行文件上传和SSE连接
        const response = await fetch('/api/upload/stream', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 解析响应获取token
        const result = await response.json();
        if (!result.token) {
            throw new Error('上传失败，未获取到token');
        }

        // 创建EventSource连接获取SSE流，带上token参数
        const es = new EventSourcePolyfill('/api/upload/stream?token=' + encodeURIComponent(result.token), {
            headers: {},
            withCredentials: false
        });

        es.onmessage = function (event) {
            handleSseEvent({type: 'message', data: event.data});
        };
        
        es.addEventListener('message', function (event) {
            handleSseEvent({type: 'message', data: event.data});
        });
        
        es.addEventListener('done', function (event) {
            handleSseEvent({type: 'done', data: event.data});
            es.close();
            resetUploadBtn();
        });
        
        es.addEventListener('error', function (event) {
            console.error('SSE连接错误:', event);
            const errorMsg = event.data || 'SSE连接失败';
            const aiMsg = renderMessage('assistant', `<span style="color:red;">文件分析失败: ${errorMsg}</span>`);
            enhanceContent(aiMsg);
            es.close();
            resetUploadBtn();
        });

        // 设置超时处理
        setTimeout(() => {
            if (es.readyState === EventSource.CONNECTING) {
                es.close();
                const aiMsg = renderMessage('assistant', '<span style="color:red;">文件分析超时，请稍后重试</span>');
                enhanceContent(aiMsg);
                resetUploadBtn();
            }
        }, 300000); // 5分钟超时

        // 用于跟踪当前处理的消息
        let currentMessageElement = null;
        let storyCount = 0;
        
        function handleSseEvent(evt) {
            try {
                const data = JSON.parse(evt.data);
                
                if (data.status === 'count') {
                    // 接收总段数信息
                    storyCount = data.total;
                    const aiMsg = renderMessage('assistant', `<div style="display:flex;align-items:center;margin-bottom:12px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-file-alt" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">文档分析</strong></div><div style="padding:12px;background:linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);border-radius:8px;border-left:4px solid #0ea5e9;"><div style="color:#0ea5e9;font-weight:bold;">文档已分割为 ${storyCount} 个故事，开始逐个分析...</div></div>`);
                    enhanceContent(aiMsg);
                    currentMessageElement = aiMsg;
                } else if (data.status === 'start') {
                    // 开始处理某个故事段
                    const idx = (typeof data.index === 'number') ? data.index + 1 : '';
                    const storyHtml = `<div style="margin-top:20px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #f59e0b 0%, #d97706 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-book-open" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">第${idx}个故事原文</strong></div><div style="padding:15px;background:#fffbeb;border-radius:8px;border-left:4px solid #f59e0b;line-height:1.6;color:#92400e;">${data.story ? data.story.replace(/\\n/g, "<br>") : '<i>无</i>'}</div><div style="margin-top:16px;padding:12px;background:linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);border-radius:8px;border:1px dashed #10b981;display:flex;flex-direction:column;align-items:center;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div class="ai-thinking-loader" style="width:24px;height:24px;margin-right:8px;"></div><span style="color:#10b981;font-weight:bold;font-size:15px;">AI正在分析中</span></div><div style="color:#065f46;font-size:13px;text-align:center;">请稍候，正在为您生成专业的教育分析...</div></div></div>`;
                    
                    if (currentMessageElement) {
                        // 检查是否已经包含相同的故事原文，避免重复添加
                        if (!currentMessageElement.innerHTML.includes(`第${idx}个故事原文`)) {
                            currentMessageElement.innerHTML += storyHtml;
                            enhanceContent(currentMessageElement);
                        }
                    } else {
                        const aiMsg = renderMessage('assistant', storyHtml);
                        enhanceContent(aiMsg);
                        currentMessageElement = aiMsg;
                    }
                } else if (data.status === 'result') {
                    // 接收AI分析结果
                    const idx = (typeof data.index === 'number') ? data.index + 1 : '';
                    let aiContent = data.ai ? data.ai.replace(/\\n/g, "<br>") : '<i>无</i>';
                    
                    // 尝试解析AI返回的JSON数据
                    let formattedContent = aiContent;
                    try {
                        const aiData = JSON.parse(data.ai);
                        if (aiData.age || aiData.original_analysis || aiData.category_selection) {
                            let formattedHtml = '';
                            if (aiData.age) {
                                formattedHtml += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-child" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">推断年龄</strong></div><div style="padding:10px 15px;background:linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);border-radius:8px;border-left:4px solid #0ea5e9;"><span style="color:#0ea5e9;font-weight:bold;font-size:18px;">${aiData.age}</span> <span style="color:#64748b;font-size:14px;">岁</span></div></div>`;
                            }
                            if (aiData.original_analysis) {
                                formattedHtml += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-brain" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">原始分析</strong></div><div style="padding:15px;background:#faf5ff;border-radius:8px;border-left:4px solid #8b5cf6;line-height:1.6;color:#4c1d95;">${aiData.original_analysis.replace(/\\n/g, '<br>')}</div></div>`;
                            }
                            if (aiData.category_selection) {
                                formattedHtml += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-tags" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">分类选择</strong></div><div style="padding:15px;background:#f0fdf4;border-radius:8px;border-left:4px solid #10b981;line-height:1.6;color:#065f46;">${aiData.category_selection.replace(/\\n/g, '<br>')}</div></div>`;
                            }
                            if (aiData.content && !aiData.original_analysis && !aiData.category_selection) {
                                formattedHtml += `<div style="padding:15px;background:#f8fafc;border-radius:8px;margin-top:8px;line-height:1.6;color:#334155;">${aiData.content.replace(/\\n/g, '<br>')}</div>`;
                            }
                            formattedContent = formattedHtml;
                        }
                    } catch (e) {
                        // 如果解析失败，使用原始内容
                        console.log('AI返回内容不是有效的JSON格式，使用原始内容');
                    }
                    
                    const resultHtml = `<div style="margin-top:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-check-circle" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">第${idx}个故事AI分析结果</strong></div><div style="padding:15px;background:#f0fdf4;border-radius:8px;border-left:4px solid #10b981;line-height:1.6;color:#065f46;">${formattedContent}</div></div>`;
                    
                    if (currentMessageElement) {
                        // 替换加载状态为结果
                        currentMessageElement.innerHTML = currentMessageElement.innerHTML.replace(
                            /<div style="margin-top:16px;padding:12px;background:linear-gradient\(135deg, #f0fdfa 0%, #ccfbf1 100%\);border-radius:8px;border:1px dashed #10b981;display:flex;flex-direction:column;align-items:center;">.*?<\/div>/s,
                            ''
                        );
                        // 确保不会重复添加结果
                        if (!currentMessageElement.innerHTML.includes(`第${idx}个故事AI分析结果`)) {
                            currentMessageElement.innerHTML += resultHtml;
                            enhanceContent(currentMessageElement);
                        }
                    }
                } else if (data.status === 'error') {
                    // 处理错误
                    const idx = (typeof data.index === 'number') ? data.index + 1 : '';
                    const errorHtml = `<div style="margin-top:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #ef4444 0%, #dc2626 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-exclamation-triangle" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">第${idx}个故事分析失败</strong></div><div style="padding:15px;background:#fef2f2;border-radius:8px;border-left:4px solid #ef4444;line-height:1.6;color:#991b1b;">${data.msg || '未知错误'}</div></div>`;
                    
                    if (currentMessageElement) {
                        // 替换加载状态为错误信息
                        currentMessageElement.innerHTML = currentMessageElement.innerHTML.replace(
                            /<div style="margin-top:16px;padding:12px;background:linear-gradient\(135deg, #f0fdfa 0%, #ccfbf1 100%\);border-radius:8px;border:1px dashed #10b981;display:flex;flex-direction:column;align-items:center;">.*?<\/div>/s,
                            ''
                        );
                        // 确保不会重复添加错误信息
                        if (!currentMessageElement.innerHTML.includes(`第${idx}个故事分析失败`)) {
                            currentMessageElement.innerHTML += errorHtml;
                            enhanceContent(currentMessageElement);
                        }
                    }
                } else if (evt.type === 'done' || data.status === 'done') {
                    // 完成信号
                    const doneHtml = `<div style="margin-top:20px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-trophy" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">分析完成</strong></div><div style="padding:15px;background:#f0fdf4;border-radius:8px;border-left:4px solid #10b981;line-height:1.6;color:#065f46;"><div style="color:#10b981;font-weight:bold;font-size:16px;">✓ 所有AI分析已完成！</div></div></div>`;
                    
                    if (currentMessageElement) {
                        currentMessageElement.innerHTML += doneHtml;
                        enhanceContent(currentMessageElement);
                    } else {
                        const aiMsg = renderMessage('assistant', doneHtml);
                        enhanceContent(aiMsg);
                    }
                }
            } catch (e) {
                console.error('SSE事件解析失败:', e, evt);
                const errorMsg = evt.data || evt.message || '未知错误';
                const aiMsg = renderMessage('assistant', `<span style="color:red;">AI分析异常: ${errorMsg}</span>`);
                enhanceContent(aiMsg);
            }
        }

        function resetUploadBtn() {
            uploadedFile = null;
            document.getElementById('file-input').value = '';
            document.getElementById('file-name').style.display = 'none';
            document.getElementById('upload-submit-btn').style.display = 'none';
            document.getElementById('upload-submit-btn').innerHTML = '<i class="fas fa-upload"></i> 上传';
            document.getElementById('upload-submit-btn').disabled = false;
            
            // 重置所有状态变量
            currentMessageElement = null;
            storyCount = 0;
        }

    } catch (error) {
        console.error('上传错误:', error);
        alert('文件上传失败: ' + error.message);
        uploadedFile = null;
        document.getElementById('file-input').value = '';
        document.getElementById('file-name').style.display = 'none';
        document.getElementById('upload-submit-btn').style.display = 'none';
        document.getElementById('upload-submit-btn').innerHTML = '<i class="fas fa-upload"></i> 上传';
        document.getElementById('upload-submit-btn').disabled = false;
    }
}

/**
 * 普通对话（非流式），一次性展示后端 /api/chat 返回的 JSON 内容
 */
async function sendMessageOnce(message) {
    const userMsg = renderMessage('user', message);
    enhanceContent(userMsg);
    const aiMsg = renderMessage('assistant', '<span id="ai-loading">AI正在思考...</span>', false);

    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message })
        });
        if (!response.ok) {
            throw new Error('AI服务响应异常');
        }
        const result = await response.json();
        if (result && result.response) {
            // 尝试解析 JSON 格式的 AI 返回
            let age = '', content = '', original_analysis = '', category_selection = '';
            try {
                const obj = JSON.parse(result.response);
                age = obj.age || '';
                content = obj.content || '';
                original_analysis = obj.original_analysis || '';
                category_selection = obj.category_selection || '';
            } catch (e) {
                content = result.response;
            }
            let html = '';
            if (age) {
                html += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-child" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">推断年龄</strong></div><div style="padding:10px 15px;background:linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);border-radius:8px;border-left:4px solid #0ea5e9;"><span style="color:#0ea5e9;font-weight:bold;font-size:18px;">${age}</span> <span style="color:#64748b;font-size:14px;">岁</span></div></div>`;
            }
            if (original_analysis) {
                html += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-brain" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">原始分析</strong></div><div style="padding:15px;background:#faf5ff;border-radius:8px;border-left:4px solid #8b5cf6;line-height:1.6;color:#4c1d95;">${original_analysis.replace(/\\n/g, '<br>')}</div></div>`;
            }
            if (category_selection) {
                html += `<div style="margin-bottom:16px;"><div style="display:flex;align-items:center;margin-bottom:8px;"><div style="width:24px;height:24px;background:linear-gradient(135deg, #10b981 0%, #059669 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:8px;"><i class="fas fa-tags" style="color:white;font-size:12px;"></i></div><strong style="font-size:16px;color:#333;">分类选择</strong></div><div style="padding:15px;background:#f0fdf4;border-radius:8px;border-left:4px solid #10b981;line-height:1.6;color:#065f46;">${category_selection.replace(/\\n/g, '<br>')}</div></div>`;
            }
            if (content && !original_analysis && !category_selection) {
                html += `<div style="padding:15px;background:#f8fafc;border-radius:8px;margin-top:8px;line-height:1.6;color:#334155;">${content.replace(/\\n/g, '<br>')}</div>`;
            }
            aiMsg.innerHTML = html;
            enhanceContent(aiMsg);
        } else {
            aiMsg.innerHTML = "<span style='color:red;'>AI服务暂不可用，请稍后再试。</span>";
        }
    } catch (err) {
        aiMsg.innerHTML = "<span style='color:red;'>AI服务暂不可用，请稍后再试。</span>";
        window.console.error('AI消息渲染异常:', err);
    }
}

// 表单提交与输入优化
const chatForm = document.getElementById('chat-form');
const input = document.getElementById('user-input');
const sendBtn = chatForm.querySelector('button[type="submit"]');
chatForm.addEventListener('submit', function (e) {
    window.console.log('表单提交事件触发');
    e.preventDefault();
    const msg = input.value.trim();
    window.console.log('用户输入内容:', msg);
    if (!msg) return;
    input.value = '';
    input.disabled = true;
    sendBtn.disabled = true;
    sendMessageOnce(msg);
    setTimeout(() => {
        input.disabled = false;
        sendBtn.disabled = false;
        input.focus();
    }, 2000); // 2秒后允许再次输入（可根据AI响应动态调整）
});
// 支持回车发送，多行输入
input.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        chatForm.dispatchEvent(new Event('submit'));
    }
});
input.focus();
</script>
</body>
</html>
