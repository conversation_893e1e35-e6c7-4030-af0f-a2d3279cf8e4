<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QQ音乐搜索</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        .nav-button.active {
            background-color: #007bff;
            color: white;
        }
        .nav-button:hover:not(.active) {
            background-color: #dee2e6;
        }
        .search-form {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
        }
        .search-input {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .search-button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .search-button:hover {
            background-color: #0056b3;
        }
        .search-types {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        .search-type {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .search-type.active {
            background-color: #007bff;
            color: white;
        }
        .results {
            margin-top: 20px;
        }
        .song-item, .album-item, .playlist-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .song-item:hover, .album-item:hover, .playlist-item:hover {
            background-color: #f9f9f9;
        }
        .song-cover, .album-cover, .playlist-cover {
            width: 50px;
            height: 50px;
            margin-right: 15px;
            object-fit: cover;
            border-radius: 3px;
        }
        .item-info {
            flex: 1;
        }
        .item-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .item-subtitle {
            color: #666;
            font-size: 14px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 10px;
        }
        .page-link {
            padding: 5px 10px;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
        }
        .page-link.active {
            background-color: #007bff;
            color: white;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .placeholder {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QQ音乐搜索</h1>
        <a href="/" class="back-link">← 返回首页</a>
        
        <div class="nav-buttons">
            <a href="/search" class="nav-button active">音乐搜索</a>
            <a href="/radio" class="nav-button">电台分类</a>
            <a href="/ai-chat" class="nav-button">🤖 AI 对话</a>
        </div>
        
        <form class="search-form" method="post" th:action="@{/search}">
            <input type="hidden" id="searchType" name="type" value="0">
            <input type="hidden" id="page" name="page" value="1">
            <input type="text" name="query" class="search-input" placeholder="请输入搜索关键词" th:value="${query}" required>
            <button type="submit" class="search-button">搜索</button>
        </form>
        
        <div class="search-types">
            <button type="button" class="search-type active" data-type="0">单曲</button>
            <button type="button" class="search-type" data-type="8">专辑</button>
            <button type="button" class="search-type" data-type="100">歌单</button>
        </div>
        
        <div class="results" th:if="${results != null}">
            <!-- 显示错误信息 -->
            <div th:if="${results.ret != null and results.ret != 0}" class="error" style="color: red; text-align: center; padding: 20px;">
                <p th:text="${'搜索失败: ' + (results.msg ?: '未知错误')}">搜索失败</p>
            </div>
            
            <!-- 显示搜索结果 -->
            <div th:if="${results.ret == null or results.ret == 0}">
                <div th:if="${(type == 0 or type == null) and results.list != null}">
                    <div class="song-item" th:each="song : ${results.list}">
                        <img th:src="${song.album_pic_300x300 ?: 'https://y.gtimg.cn/mediastyle/global/img/song_300.png'}" alt="歌曲封面" class="song-cover">
                        <div class="item-info">
                            <div class="item-title" th:text="${song.song_name ?: '未知歌曲'}"></div>
                            <div class="item-subtitle" th:text="${song.singer_name ?: '未知歌手'}"></div>
                        </div>
                        <!-- 播放器JS依赖此属性，需渲染song_mid -->
                        <span th:attr="data-song-mid=${song.song_mid}" style="display:none"></span>
                    </div>
                </div>
                
                <div th:if="${type == 8 and results.list != null}">
                    <div class="album-item" th:each="album : ${results.list}">
                        <img th:src="${album.album_pic_300x300 ?: 'https://y.gtimg.cn/mediastyle/global/img/album_300.png'}" alt="专辑封面" class="album-cover">
                        <div class="item-info">
                            <div class="item-title" th:text="${album.album_name ?: '未知专辑'}"></div>
                            <div class="item-subtitle" th:text="${album.singer_name ?: '未知歌手'}"></div>
                        </div>
                    </div>
                </div>
                
                    <div class="playlist-item" th:each="playlist : ${results.list}">
                        <img th:src="${playlist.logo ?: 'https://y.gtimg.cn/mediastyle/global/img/playlist_300.png'}" alt="歌单封面" class="playlist-cover">
                        <div class="item-info">
                            <div class="item-title">
                                <a th:href="@{/songlist/{dissid}(dissid=${playlist.dissid})}" 
                                   th:text="${playlist.dissname ?: '未知歌单'}" 
                                   style="text-decoration: none; color: #333;"></a>
                            </div>
                            <div class="item-subtitle" th:text="${playlist.nickname ?: '未知用户'} + ' · ' + ${playlist.songnum ?: 0} + '首歌'"></div>
                        </div>
                    </div>
                </div>
                
                <div class="pagination" th:if="${results.list != null and !results.list.isEmpty()}">
                    <a href="#" class="page-link" th:if="${page > 1}" th:onclick="'changePage(' + ${page - 1} + ')'">上一页</a>
                    <span class="page-link active" th:text="${page}"></span>
                    <a href="#" class="page-link" th:if="${results.curnum == 20 or results.cur_num == 20}" th:onclick="'changePage(' + ${page + 1} + ')'">下一页</a>
                </div>
            </div>
        </div>
        <!-- 音乐播放器组件 -->
        <audio id="music-player" controls style="width:100%;margin-top:20px;display:none"></audio>
        
        <div class="placeholder" th:if="${results == null}">
            <p>请输入关键词开始搜索</p>
        </div>
    </div>

    <script th:inline="javascript">
        /*<![CDATA[*/
        // 搜索类型切换
        document.querySelectorAll('.search-type').forEach(type => {
            type.addEventListener('click', function() {
                document.querySelectorAll('.search-type').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                document.getElementById('searchType').value = this.getAttribute('data-type');
                document.getElementById('page').value = 1;
                // 移除自动提交，只更新选中状态和隐藏字段值
            });
        });
        
        // 分页
        function changePage(page) {
            document.getElementById('page').value = page;
            document.querySelector('.search-form').submit();
        }
        /*]]>*/
    </script>
    <script>
        // 歌曲播放功能
        document.addEventListener('DOMContentLoaded', function() {
            // 事件委托，支持动态渲染
            document.querySelectorAll('.song-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    // 取消其他高亮
                    document.querySelectorAll('.song-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    // 获取歌曲mid（假设后端返回song_mid字段，若无请补充）
                    let songName = this.querySelector('.item-title').textContent.trim();
                    let singerName = this.querySelector('.item-subtitle').textContent.trim();
                    // 通过th:each="song : ${results.list}"，song.song_mid应可用
                    let songMid = this.getAttribute('th:each') ? null : this.getAttribute('data-song-mid');
                    // Thymeleaf渲染时可加data-song-mid属性
                    if (!songMid) {
                        // 尝试从隐藏字段或data属性获取
                        let midElem = this.querySelector('[data-song-mid]');
                        if (midElem) songMid = midElem.getAttribute('data-song-mid');
                    }
                    if (!songMid) {
                        // 兼容th:each渲染，直接从后端渲染data-song-mid
                        songMid = this.getAttribute('data-song-mid');
                    }
                    if (!songMid) {
                        alert('无法获取歌曲ID，无法播放');
                        return;
                    }
                    // 请求播放URL
                    fetch('/api/playurl/' + songMid)
                        .then(resp => resp.json())
                        .then(data => {
                            if (data.playUrl) {
                                let player = document.getElementById('music-player');
                                player.src = data.playUrl;
                                player.style.display = 'block';
                                player.play();
                            } else {
                                alert(data.error || '无法获取播放地址');
                            }
                        })
                        .catch(() => alert('请求播放地址失败'));
                });
            });
        });
    </script>
</body>
</html>