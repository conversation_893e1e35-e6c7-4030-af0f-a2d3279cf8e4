package com.ywcx.java_qqmusic_demo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.output.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class IntentRecognitionService {
    
    private static final Logger logger = LoggerFactory.getLogger(IntentRecognitionService.class);
    
    private ChatLanguageModel model;
    
    @Value("${intent-recognition-prompt:请分析用户输入的意图，如果是音乐相关请求（如播放歌曲、搜索音乐、查找歌手等）请回答'音乐'，如果是其他问题请回答'问题'。}")
    private String intentRecognitionPrompt;
    
    public IntentRecognitionService() {
        // 尝试初始化模型，如果失败则保持为null
        try {
            // 注意：需要设置OPENAI_API_KEY环境变量才能使用OpenAI模型
            String apiKey = System.getenv("OPENAI_API_KEY");
            if (apiKey != null && !apiKey.isEmpty()) {
                this.model = OpenAiChatModel.withApiKey(apiKey);
            }
        } catch (Exception e) {
            // 如果初始化失败，model保持为null，服务仍然可用
            System.err.println("AI模型初始化失败: " + e.getMessage());
        }
        logger.info("IntentRecognitionService 初始化完成");
    }
    
    public UserIntent recognizeIntent(String userMessage) {
        try {
            logger.info("开始识别用户意图: {}", userMessage);
            
            if (model == null) {
                logger.warn("AI模型未正确初始化，使用规则识别");
                return recognizeByRules(userMessage);
            }
            
            if (userMessage == null || userMessage.trim().isEmpty()) {
                logger.warn("用户消息为空");
                return UserIntent.QUESTION;
            }
            
            // 首先使用规则快速识别
            UserIntent ruleBasedIntent = recognizeByRules(userMessage);
            if (ruleBasedIntent != null) {
                logger.info("基于规则识别到意图: {}", ruleBasedIntent);
                return ruleBasedIntent;
            }
            
            // 使用AI模型进行意图识别
            List<dev.langchain4j.data.message.ChatMessage> messages = new ArrayList<>();
            messages.add(new SystemMessage(intentRecognitionPrompt));
            messages.add(new UserMessage("请分析以下用户输入的意图：\n" + userMessage));
            
            logger.info("发送消息到AI模型进行意图识别");
            Response<dev.langchain4j.data.message.AiMessage> response = model.generate(messages);
            
            if (response == null || response.content() == null) {
                logger.error("AI模型返回空响应");
                return UserIntent.QUESTION;
            }
            
            String result = response.content().text().trim();
            logger.info("AI模型返回意图识别结果: {}", result);
            
            // 解析AI返回的意图
            return parseIntentFromAIResponse(result);
            
        } catch (Exception e) {
            logger.error("意图识别时发生错误: ", e);
            // 出错时回退到规则识别
            return recognizeByRules(userMessage);
        }
    }
    
    private UserIntent recognizeByRules(String userMessage) {
        String message = userMessage.toLowerCase();
        
        // 音乐相关关键词
        String[] musicKeywords = {
            "播放", "听", "歌曲", "音乐", "歌", "sing", "music", "play", 
            "搜索", "找", "search", "歌手", "artist", "专辑", "album",
            "歌单", "playlist", "歌词", "lyric", "电台", "radio"
        };
        
        for (String keyword : musicKeywords) {
            if (message.contains(keyword)) {
                return UserIntent.MUSIC;
            }
        }
        
        return UserIntent.QUESTION;
    }
    
    private UserIntent parseIntentFromAIResponse(String aiResponse) {
        // 尝试从AI响应中提取意图
        if (aiResponse.contains("音乐") || aiResponse.contains("播放") || 
            aiResponse.contains("听歌") || aiResponse.contains("歌曲") ||
            aiResponse.toLowerCase().contains("music") || 
            aiResponse.toLowerCase().contains("play")) {
            return UserIntent.MUSIC;
        }
        
        return UserIntent.QUESTION;
    }
    
    public MusicRequest parseMusicRequest(String userMessage) {
        try {
            logger.info("解析音乐请求: {}", userMessage);
            
            String message = userMessage.toLowerCase();
            
            // 提取歌曲名称
            String songName = extractSongName(message);
            
            // 提取歌手名称
            String artistName = extractArtistName(message);
            
            MusicRequest request = new MusicRequest();
            request.setOriginalMessage(userMessage);
            request.setSongName(songName);
            request.setArtistName(artistName);
            request.setSearchType(determineSearchType(message));
            
            logger.info("解析音乐请求结果 - 歌曲: {}, 歌手: {}, 搜索类型: {}", 
                       songName, artistName, request.getSearchType());
            
            return request;
            
        } catch (Exception e) {
            logger.error("解析音乐请求时发生错误: ", e);
            return new MusicRequest(userMessage, "", "", 0);
        }
    }
    
    private String extractSongName(String message) {
        // 常见的歌曲名称提取模式
        Pattern[] patterns = {
            Pattern.compile("播放[\\s]*(.*?)[\\s]*的歌"),
            Pattern.compile("听[\\s]*(.*?)[\\s]*的"),
            Pattern.compile("搜索[\\s]*(.*?)[\\s]*歌曲"),
            Pattern.compile("找[\\s]*(.*?)[\\s]*这首歌"),
            Pattern.compile("play[\\s]*(.*?)"),
            Pattern.compile("我想听[\\s]*一首?[\\s]*(.*?)[\\s]*$"),
            Pattern.compile("我想听[\\s]*一首?[\\s]*(.*?)[\\s]*[的首歌]")
        };
        
        for (Pattern pattern : patterns) {
            Matcher matcher = pattern.matcher(message);
            if (matcher.find()) {
                String songName = matcher.group(1).trim();
                if (!songName.isEmpty()) {
                    return songName;
                }
            }
        }
        
        // 如果没有匹配到模式，尝试提取引号中的内容
        Pattern quotePattern = Pattern.compile("[\"']([^\"']+)[\"']");
        Matcher quoteMatcher = quotePattern.matcher(message);
        if (quoteMatcher.find()) {
            return quoteMatcher.group(1).trim();
        }
        
        // 如果还是没有找到，移除常见的前缀词后返回剩余内容
        String[] prefixes = {"我想听", "播放", "听", "搜索", "找", "play"};
        String remaining = message;
        for (String prefix : prefixes) {
            if (remaining.startsWith(prefix)) {
                remaining = remaining.substring(prefix.length()).trim();
                break;
            }
        }
        
        // 移除后缀词
        String[] suffixes = {"的歌", "这首歌", "歌曲", "的歌"};
        for (String suffix : suffixes) {
            if (remaining.endsWith(suffix)) {
                remaining = remaining.substring(0, remaining.length() - suffix.length()).trim();
                break;
            }
        }
        
        return remaining.isEmpty() ? "" : remaining;
    }
    
    private String extractArtistName(String message) {
        // 常见的歌手名称提取模式
        Pattern[] patterns = {
            Pattern.compile(".*?[的|歌手|artist][\\s]*(.*?)[\\s]*(歌|歌曲|music)"),
            Pattern.compile("(.*?)[的|唱][\\s]*歌"),
            Pattern.compile("歌手[\\s]*(.*?)[\\s]*的")
        };
        
        for (Pattern pattern : patterns) {
            Matcher matcher = pattern.matcher(message);
            if (matcher.find()) {
                String artistName = matcher.group(1).trim();
                if (!artistName.isEmpty()) {
                    return artistName;
                }
            }
        }
        
        return "";
    }
    
    private int determineSearchType(String message) {
        if (message.contains("歌手") || message.contains("artist")) {
            return 1; // 搜索歌手
        } else if (message.contains("专辑") || message.contains("album")) {
            return 2; // 搜索专辑
        } else if (message.contains("歌单") || message.contains("playlist")) {
            return 3; // 搜索歌单
        } else if (message.contains("歌词") || message.contains("lyric")) {
            return 4; // 搜索歌词
        } else {
            return 0; // 搜索歌曲
        }
    }
    
    public enum UserIntent {
        MUSIC,    // 音乐相关意图
        QUESTION  // 问题咨询意图
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MusicRequest {
        private String originalMessage;
        private String songName;
        private String artistName;
        private int searchType;
        
        public MusicRequest() {}
        
        public MusicRequest(String originalMessage, String songName, String artistName, int searchType) {
            this.originalMessage = originalMessage;
            this.songName = songName;
            this.artistName = artistName;
            this.searchType = searchType;
        }
        
        @JsonProperty("originalMessage")
        public String getOriginalMessage() {
            return originalMessage;
        }
        
        public void setOriginalMessage(String originalMessage) {
            this.originalMessage = originalMessage;
        }
        
        @JsonProperty("songName")
        public String getSongName() {
            return songName;
        }
        
        public void setSongName(String songName) {
            this.songName = songName;
        }
        
        @JsonProperty("artistName")
        public String getArtistName() {
            return artistName;
        }
        
        public void setArtistName(String artistName) {
            this.artistName = artistName;
        }
        
        @JsonProperty("searchType")
        public int getSearchType() {
            return searchType;
        }
        
        public void setSearchType(int searchType) {
            this.searchType = searchType;
        }
    }
}