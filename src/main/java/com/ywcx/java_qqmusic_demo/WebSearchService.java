package com.ywcx.java_qqmusic_demo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.stream.Collectors;

/**
 * 简单的联网搜索服务，默认使用 Bing Web Search API
 * 可根据需要扩展为其他搜索引擎
 */
@Service
public class WebSearchService {

    @Value("${bing.api.key:}")
    private String bingApiKey;

    private static final String BING_ENDPOINT = "https://api.bing.microsoft.com/v7.0/search";

    /**
     * 联网搜索，返回前3条摘要拼接
     */
    public String searchWeb(String query) {
        if (bingApiKey == null || bingApiKey.isEmpty()) {
            return "【未配置Bing API KEY，无法联网搜索】";
        }
        try {
            String url = UriComponentsBuilder.fromHttpUrl(BING_ENDPOINT)
                    .queryParam("q", query)
                    .queryParam("count", 3)
                    .build().toUriString();

            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestProperty("Ocp-Apim-Subscription-Key", bingApiKey);
            conn.setRequestProperty("Accept", "application/json");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);

            int code = conn.getResponseCode();
            if (code != 200) {
                return "【联网搜索失败，状态码：" + code + "】";
            }
            String json = new BufferedReader(new InputStreamReader(conn.getInputStream()))
                    .lines().collect(Collectors.joining("\n"));

            // 简单提取摘要（可用更强的JSON解析器优化）
            StringBuilder sb = new StringBuilder();
            int idx = 0;
            for (String item : json.split("\"snippet\":\"")) {
                if (idx++ == 0) continue;
                String snippet = item.split("\"")[0].replace("\\n", " ").replace("\\\"", "\"");
                sb.append("• ").append(snippet).append("\n");
                if (idx > 3) break;
            }
            if (sb.length() == 0) {
                return "【未检索到相关实时信息】";
            }
            return "【联网搜索结果】\n" + sb.toString().trim();
        } catch (Exception e) {
            return "【联网搜索异常：" + e.getMessage() + "】";
        }
    }
}