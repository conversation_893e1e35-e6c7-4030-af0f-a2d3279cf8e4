package com.ywcx.java_qqmusic_demo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class IntentProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(IntentProcessingService.class);
    
    @Autowired
    private IntentRecognitionService intentRecognitionService;
    
    @Autowired
    private QQMusicService qqMusicService;
    
    @Autowired
    private AiChatService aiChatService;
    
    public String processUserRequest(String userMessage) {
        try {
            logger.info("开始处理用户请求: {}", userMessage);
            
            // 识别用户意图
            IntentRecognitionService.UserIntent intent = intentRecognitionService.recognizeIntent(userMessage);
            logger.info("识别到用户意图: {}", intent);
            
            switch (intent) {
                case MUSIC:
                    return handleMusicRequest(userMessage);
                case QUESTION:
                    return handleQuestionRequest(userMessage);
                default:
                    return handleQuestionRequest(userMessage);
            }
            
        } catch (Exception e) {
            logger.error("处理用户请求时发生错误: ", e);
            return "抱歉，处理您的请求时发生了错误。请稍后再试。";
        }
    }
    
    private String handleMusicRequest(String userMessage) {
        try {
            logger.info("处理音乐请求: {}", userMessage);
            
            // 解析音乐请求
            IntentRecognitionService.MusicRequest musicRequest = intentRecognitionService.parseMusicRequest(userMessage);
            logger.info("解析的音乐请求 - 原始消息: {}, 歌曲名: {}, 歌手名: {}, 搜索类型: {}", 
                       musicRequest.getOriginalMessage(), musicRequest.getSongName(), 
                       musicRequest.getArtistName(), musicRequest.getSearchType());
            
            // 如果没有提取到具体的歌曲或歌手信息，尝试搜索整个消息
            String searchQuery = musicRequest.getSongName();
            if (searchQuery.isEmpty()) {
                searchQuery = musicRequest.getArtistName();
            }
            if (searchQuery.isEmpty()) {
                searchQuery = musicRequest.getOriginalMessage();
            }
            
            logger.info("最终搜索词: {}", searchQuery);
            
            if (searchQuery.isEmpty()) {
                return "请问您想听什么歌曲呢？请告诉我歌曲名称或歌手名字。";
            }
            
            // 搜索音乐
            Map<String, Object> searchResult = qqMusicService.searchMusic(searchQuery, musicRequest.getSearchType(), 1, 10);
            logger.info("搜索结果: {}", searchResult);
            
            // 处理搜索结果
            return formatMusicSearchResult(searchResult, searchQuery);
            
        } catch (Exception e) {
            logger.error("处理音乐请求时发生错误: ", e);
            return "抱歉，搜索音乐时发生了错误。请稍后再试。";
        }
    }
    
    private String handleQuestionRequest(String userMessage) {
        try {
            logger.info("处理问题请求: {}", userMessage);
            return aiChatService.chat(userMessage);
        } catch (Exception e) {
            logger.error("处理问题请求时发生错误: ", e);
            return "抱歉，我无法回答您的问题。";
        }
    }
    
    private String formatMusicSearchResult(Map<String, Object> searchResult, String searchQuery) {
        try {
            logger.info("[格式化搜索结果] 开始格式化 - 搜索词: {}, 结果: {}", searchQuery, searchResult);
            
            if (searchResult == null || searchResult.isEmpty()) {
                logger.warn("[格式化搜索结果] 搜索结果为空");
                return "抱歉，没有找到相关的音乐内容。";
            }
            
            Object ret = searchResult.get("ret");
            if (ret != null && !ret.equals(0)) {
                logger.warn("[格式化搜索结果] 搜索返回错误码: {}", ret);
                return "抱歉，搜索音乐时发生了错误。请稍后再试。";
            }
            
            Object data = searchResult.get("data");
            if (data == null) {
                logger.warn("[格式化搜索结果] 搜索结果中没有data字段");
                return "抱歉，没有找到与\"" + searchQuery + "\"相关的音乐内容。";
            }
            
            // 这里简化处理，实际应该根据返回的数据结构进行解析
            StringBuilder response = new StringBuilder();
            response.append("为您找到以下相关音乐：\n\n");
            
            // 假设返回的是列表格式
            if (data instanceof List) {
                List<?> items = (List<?>) data;
                int maxResults = Math.min(items.size(), 5); // 最多显示5个结果
                
                for (int i = 0; i < maxResults; i++) {
                    Object item = items.get(i);
                    response.append(i + 1).append(". ");
                    
                    // 尝试提取歌曲信息
                    if (item instanceof Map) {
                        Map<?, ?> itemMap = (Map<?, ?>) item;
                        String songName = getSafeString(itemMap.get("songName"));
                        String singerName = getSafeString(itemMap.get("singerName"));
                        
                        if (!songName.isEmpty() && !singerName.isEmpty()) {
                            response.append(songName).append(" - ").append(singerName);
                        } else {
                            response.append("歌曲").append(i + 1);
                        }
                    } else {
                        response.append("歌曲").append(i + 1);
                    }
                    
                    response.append("\n");
                }
                
                if (items.size() > maxResults) {
                    response.append("\n还有 ").append(items.size() - maxResults).append(" 个相关结果...");
                }
            } else {
                response.append("找到了一些相关的音乐内容，但无法显示详细信息。");
            }
            
            response.append("\n\n请告诉我您想听哪一首歌？");
            
            return response.toString();
            
        } catch (Exception e) {
            logger.error("格式化音乐搜索结果时发生错误: ", e);
            return "找到了相关的音乐内容，但显示详细信息时发生了错误。";
        }
    }
    
    private String getSafeString(Object obj) {
        return obj != null ? obj.toString() : "";
    }
}