package com.ywcx.java_qqmusic_demo;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import org.springframework.util.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.Desktop;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.lang.reflect.Type;

public class QQMusicClient {
    
    private static final Logger logger = LoggerFactory.getLogger(QQMusicClient.class);

    // === 基本配置信息 ===
    private static final String APP_ID = "2000001390";       // 真实应用ID
    private static final String APP_KEY = "TeciPrSXHcIhZsPO";  // 真实密钥
    private static final String BASE_URL = "https://test.szhkeji.com/rpc_proxy/music_open_api";
    private static final String DEVICE_ID = "FIXED_DEV_0001";  // 设备ID
    private static final String CLIENT_IP = "*************";          // 实际公网IP (示例用)

    private static final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    
    private static final Gson gson = new Gson();

    public static void main(String[] args) {
        QQMusicClient client = new QQMusicClient();
        client.run();
    }

    public void run() {
        try {
            // 获取设备票据
            Map<String, String> deviceToken = getDeviceToken();
            if (deviceToken == null) {
                logger.error("获取设备票据失败！");
                return;
            }

            String opiDeviceId = deviceToken.get("opi_device_id");
            String opiDeviceKey = deviceToken.get("opi_device_key");

            // 获取登录二维码
            Map<String, String> loginResult = getLoginQrCode(opiDeviceId, opiDeviceKey);
            if (loginResult == null) {
                logger.error("获取登录二维码失败！");
                return;
            }

            String authCode = loginResult.get("auth_code");

            // 轮询授权结果
            String encryptString = pollAuthResult(authCode, opiDeviceId, opiDeviceKey);
            if (encryptString == null) {
                logger.error("轮询授权结果超时或失败！");
                return;
            }


            // 获取访问令牌
            Map<String, String> tokenInfo = getAccessToken(encryptString, opiDeviceId, opiDeviceKey);
            // 如果获取访问令牌失败（例如，因为refresh_token过期），提示用户重新登录
            while (tokenInfo == null) {
                logger.warn("将重新开始登录流程，请稍候…");
                loginResult = getLoginQrCode(opiDeviceId, opiDeviceKey);
                if (loginResult == null) {
                    logger.error("无法生成新的登录二维码，退出程序！");
                    return;
                }

                authCode = loginResult.get("auth_code");
                encryptString = pollAuthResult(authCode, opiDeviceId, opiDeviceKey);
                if (encryptString == null) {
                    logger.error("未能完成用户登录验证，退出程序！");
                    return;
                }

                tokenInfo = getAccessToken(encryptString.substring(5), opiDeviceId, opiDeviceKey);
            }

            String openId = tokenInfo.get("open_id");
            String accessToken = tokenInfo.get("access_token");

            // 搜索音乐功能
            searchMusic("七里香", 0, 1, 10, openId, accessToken, opiDeviceId, opiDeviceKey);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成签名
     */
    private static String generateSign(Map<String, String> params, String appKey) {
        logger.debug("[生成签名] 入参 - params: {}, appKey: {}", params, appKey);
        
        List<Map.Entry<String, String>> sortedParams = new ArrayList<>(params.entrySet());
        sortedParams.sort(Map.Entry.comparingByKey());

        StringBuilder signStr = new StringBuilder();
        for (int i = 0; i < sortedParams.size(); i++) {
            Map.Entry<String, String> entry = sortedParams.get(i);
            if (i > 0) {
                signStr.append("&");
            }
            signStr.append(entry.getKey()).append("=").append(entry.getValue());
        }
        signStr.append("_").append(appKey);
        
        String signature = DigestUtils.md5DigestAsHex(signStr.toString().getBytes(StandardCharsets.UTF_8));
        logger.debug("[生成签名] 签名字符串: {}, 生成的签名: {}", signStr.toString(), signature);
        
        return signature;
    }

    /**
     * 构造公共参数
     */
    private static Map<String, String> buildCommonParams(String opiCmd, Map<String, String> additionalParams) {
        long timestamp = System.currentTimeMillis() / 1000;
        Map<String, String> params = new HashMap<>();
        params.put("app_id", APP_ID);
        params.put("timestamp", String.valueOf(timestamp));
        params.put("device_id", DEVICE_ID);
        params.put("opi_cmd", opiCmd);
        params.put("client_ip", CLIENT_IP);
        
        if (additionalParams != null) {
            params.putAll(additionalParams);
        }
        
        return params;
    }

    /**
     * 请求设备票据
     */
    public Map<String, String> getDeviceToken() {
        logger.info("[获取设备票据] 开始请求设备票据");
        
        Map<String, String> params = buildCommonParams("CreateDeviceToken", null);
        params.put("sign", generateSign(params, APP_KEY));
        
        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[获取设备票据] 请求URL: {}", url);
        
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取设备票据] 响应状态码: {}", response.statusCode());
            logger.info("[获取设备票据] 响应内容: {}", response.body());
            
            Map<String, Object> responseMap = parseJson(response.body());
            
            if (((Number) responseMap.getOrDefault("ret", -1)).intValue() == 0) {
                Map<String, Object> tokenInfo = (Map<String, Object>) responseMap.get("token_info");
                String deviceId = (String) tokenInfo.get("opi_device_id");
                String deviceKey = (String) tokenInfo.get("opi_device_key");
                
                logger.info("[获取设备票据] 成功获取设备票据 - deviceId: {}, deviceKey: {}", deviceId, deviceKey);
                
                Map<String, String> result = new HashMap<>();
                result.put("opi_device_id", deviceId);
                result.put("opi_device_key", deviceKey);
                return result;
            } else {
                String msg = (String) responseMap.getOrDefault("msg", "未知错误");
                Integer ret = ((Number) responseMap.getOrDefault("ret", -1)).intValue();
                logger.error("[获取设备票据] 失败 - ret: {}, msg: {}", ret, msg);
                return null;
            }
        } catch (Exception e) {
            logger.error("[获取设备票据] 异常", e);
            return null;
        }
    }

    /**
     * 请求并返回用于登录的二维码及其授权码(auth_code)
     */
    public Map<String, String> getLoginQrCode(String opiDeviceId, String opiDeviceKey) {
        logger.info("[获取登录二维码] 开始请求 - opiDeviceId: {}, opiDeviceKey: {}", opiDeviceId, opiDeviceKey);
        
        Map<String, String> encryptAuthMap = new HashMap<>();
        encryptAuthMap.put("response_type", "code");
        encryptAuthMap.put("state", "123456");
        encryptAuthMap.put("callbackUrl", "https://y.qq.com");
        
        String encryptAuth = mapToJson(encryptAuthMap);
        logger.debug("[获取登录二维码] 加密认证信息: {}", encryptAuth);
        
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("qqmusic_qrcode_type", "universal");
        additionalParams.put("qqmusic_encrypt_auth", encryptAuth);
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        
        Map<String, String> params = buildCommonParams("fcg_music_custom_sdk_get_qr_code.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));
        
        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[获取登录二维码] 请求URL: {}", url);
        
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取登录二维码] 响应状态码: {}", response.statusCode());
            logger.info("[获取登录二维码] 响应内容: {}", response.body());
            
            Map<String, Object> responseMap = parseJson(response.body());
            
            if (((Number) responseMap.getOrDefault("ret", -1)).intValue() == 0) {
                String qrUrl = (String) responseMap.get("sdk_qr_code");
                String authCode = (String) responseMap.get("auth_code");
                
                logger.info("[获取登录二维码] 成功获取二维码 - authCode: {}, qrUrl: {}", authCode, qrUrl);
                
                generateQrImage(qrUrl, "qq_music_login_qr.png");
                logger.info("请立即用QQ音乐/QQ/微信扫码");
                
                Map<String, String> result = new HashMap<>();
                result.put("auth_code", authCode);
                result.put("qr_url", qrUrl);
                return result;
            } else {
                String msg = (String) responseMap.getOrDefault("msg", "未知错误");
                Integer ret = ((Number) responseMap.getOrDefault("ret", -1)).intValue();
                logger.error("[获取登录二维码] 失败 - ret: {}, msg: {}", ret, msg);
                return null;
            }
        } catch (Exception e) {
            logger.error("[获取登录二维码] 异常", e);
            return null;
        }
    }

    /**
     * 生成二维码图片
     */
    private void generateQrImage(String url, String filename) {
        try {
            // 使用ZXing库生成二维码并保存到本地文件
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            
            BitMatrix bitMatrix = qrCodeWriter.encode(url, BarcodeFormat.QR_CODE, 300, 300, hints);
            
            // 创建BufferedImage
            BufferedImage image = new BufferedImage(300, 300, BufferedImage.TYPE_INT_RGB);
            for (int x = 0; x < 300; x++) {
                for (int y = 0; y < 300; y++) {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
                }
            }
            
            // 保存二维码图片到本地文件
            File qrFile = new File(filename);
            ImageIO.write(image, "PNG", qrFile);
            
            logger.info("二维码已生成并保存到本地文件: {}", qrFile.getAbsolutePath());
            logger.info("二维码URL: {}", url);
            
            // 尝试在系统默认图片查看器中打开二维码
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().open(qrFile);
            }
        } catch (WriterException | IOException e) {
            logger.error("生成二维码失败", e);
            
            // 如果生成二维码失败，回退到原来的行为
            try {
                logger.warn("回退到浏览器显示二维码...");
                if (Desktop.isDesktopSupported()) {
                    Desktop.getDesktop().browse(URI.create(url));
                }
            } catch (IOException ioException) {
                logger.error("无法打开浏览器", ioException);
            }
        }
    }

    /**
     * 轮询授权结果
     */
    public String pollAuthResult(String authCode, String opiDeviceId, String opiDeviceKey) {
        logger.info("[轮询授权结果] 开始轮询 - authCode: {}, opiDeviceId: {}, opiDeviceKey: {}", authCode, opiDeviceId, opiDeviceKey);
        
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("qqmusic_openid_authCode", authCode);
        additionalParams.put("state", String.valueOf(System.currentTimeMillis() / 1000));
        
        Map<String, String> params = buildCommonParams("fcg_music_custom_qrcode_auth_poll.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));
        
        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[轮询授权结果] 轮询URL: {}", url);
        
        logger.info("\n" + "=".repeat(50));
        logger.info(" 等待用户扫码授权...");
        logger.info(" 请使用QQ音乐/QQ/微信扫描二维码");
        logger.info(" 授权有效期：5分钟");
        logger.info("=".repeat(50));
        
        int maxAttempts = 60;
        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            try {
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .GET()
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                logger.debug("[轮询授权结果] 第{}次轮询 - 状态码: {}, 响应: {}", attempt + 1, response.statusCode(), response.body());
                
                Map<String, Object> data = parseJson(response.body());
                
                if (((Number) data.getOrDefault("ret", -1)).intValue() == 0) {
                    String encryptString = (String) data.get("encryptString");
                    logger.info("[轮询授权结果] 扫码授权成功 - encryptString: {}", encryptString);
                    logger.info("\n 扫码授权成功！");
                    return encryptString;
                } else if (Arrays.asList(-19, -10, -14, -11).contains(data.get("ret"))) {
                    // 只有真正的错误才退出，"pls scan qrcode first."是正常等待状态
                    String errorMsg = (String) data.getOrDefault("msg", "");
                    if (!"pls scan qrcode first.".equals(errorMsg)) {
                        logger.error("[轮询授权结果] 授权失败 - ret: {}, msg: {}", data.get("ret"), errorMsg);
                        logger.error("\n 授权失败: {}", errorMsg);
                        break;
                    }
                }
                
                // 显示等待进度
                int remainingTime = (maxAttempts - attempt - 1) * 5;
                int minutes = remainingTime / 60;
                int seconds = remainingTime % 60;
                logger.info("\r 等待扫码中... 剩余时间: %02d:%02d (第%d/%d次检查)", minutes, seconds, attempt+1, maxAttempts);
                
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                logger.warn("[轮询授权结果] 轮询被中断");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("[轮询授权结果] 第{}次轮询异常", attempt + 1, e);
            }
        }
        
        logger.warn("[轮询授权结果] 等待超时，轮询结束");
        logger.warn("\n 等待超时，请重新运行程序");
        return null;
    }

    /**
     * 通过code换取access token
     */
    public Map<String, String> getAccessToken(String code, String opiDeviceId, String opiDeviceKey) {
        logger.info("[获取访问令牌] 开始请求 - code: {}, opiDeviceId: {}, opiDeviceKey: {}", code, opiDeviceId, opiDeviceKey);
        
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("cmd", "getToken");
        additionalParams.put("code", code);
        
        Map<String, String> params = buildCommonParams("fcg_music_oauth_get_accesstoken.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));
        
        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[获取访问令牌] 请求URL: {}", url);
        
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取访问令牌] 响应状态码: {}", response.statusCode());
            logger.info("[获取访问令牌] 响应内容: {}", response.body());
            
            Map<String, Object> responseMap = parseJson(response.body());
            
            if (((Number) responseMap.getOrDefault("ret", -1)).intValue() == 0) {
                String encryptString = (String) responseMap.get("encryptString");
                logger.debug("[获取访问令牌] 加密字符串: {}", encryptString);
                
                Map<String, Object> tokenInfo = parseJson(encryptString);
                
                String openId = (String) tokenInfo.get("qqmusic_open_id");
                String accessToken = (String) tokenInfo.get("qqmusic_access_token");
                
                logger.info("[获取访问令牌] 成功获取令牌 - openId: {}, accessToken: {}...", openId, accessToken.substring(0, Math.min(10, accessToken.length())));
                
                Map<String, String> result = new HashMap<>();
                result.put("open_id", openId);
                result.put("access_token", accessToken);
                return result;
            } else if (((Number) responseMap.getOrDefault("ret", 0)).intValue() == -13) {
                Integer ret = ((Number) responseMap.get("ret")).intValue();
                String msg = (String) responseMap.getOrDefault("msg", "未知错误");
                logger.warn("[获取访问令牌] 令牌过期或无效 - ret: {}, msg: {}", ret, msg);
                return null;
            } else if (((Number) responseMap.getOrDefault("ret", 0)).intValue() == -10) {
                Integer ret = ((Number) responseMap.get("ret")).intValue();
                String msg = (String) responseMap.getOrDefault("msg", "未知错误");
                logger.warn("[获取访问令牌] 参数错误 - ret: {}, msg: {}", ret, msg);
                return null;
            } else {
                String msg = (String) responseMap.getOrDefault("msg", "未知错误");
                Integer ret = ((Number) responseMap.get("ret")).intValue();
                logger.error("[获取访问令牌] 失败 - ret: {}, msg: {}", ret, msg);
                return null;
            }
        } catch (Exception e) {
            logger.error("[获取访问令牌] 异常", e);
            return null;
        }
    }

    /**
     * 根据关键词和类型搜索音乐
     */
    public Map<String, Object> searchMusic(String query, int searchType, int pageNo, int pageSize, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        logger.info("[搜索音乐] 开始搜索 - query: {}, searchType: {}, pageNo: {}, pageSize: {}, openId: {}, accessToken: {}...", 
                   query, searchType, pageNo, pageSize, openId, accessToken.substring(0, Math.min(10, accessToken.length())));
        
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("w", query);
        additionalParams.put("t", String.valueOf(searchType));
        additionalParams.put("p", String.valueOf(pageNo));
        additionalParams.put("num", String.valueOf(pageSize));

        // 关键：添加设备登录态参数
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_search.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[搜索音乐] 请求URL: {}", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[搜索音乐] 响应状态码: {}", response.statusCode());
            logger.info("[搜索音乐] 响应内容长度: {}", response.body().length());
            logger.debug("[搜索音乐] 响应内容: {}", response.body());
            
            Map<String, Object> result = parseJson(response.body());
            logger.info("[搜索音乐] 解析结果 - ret: {}", result.get("ret"));
            
            return result;
        } catch (Exception e) {
            logger.error("[搜索音乐] 异常", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 搜索歌单 - 使用 fcg_songlist_search.fcg 接口
     * API文档要求：w(搜索词必传), p(页码1-2可选), num(每页大小最大10可选)
     */
    public Map<String, Object> searchSonglist(String query, int pageNo, int pageSize, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        logger.info("[搜索歌单] 开始搜索 - query: {}, pageNo: {}, pageSize: {}, openId: {}, accessToken: {}...", 
                   query, pageNo, pageSize, openId, accessToken.substring(0, Math.min(10, accessToken.length())));
        
        // 确保参数符合API限制
        int validPageNo = Math.max(1, Math.min(pageNo, 2)); // 页码1-2
        int validPageSize = Math.max(1, Math.min(pageSize, 10)); // 每页1-10
        
        logger.debug("[搜索歌单] 参数校验 - 原始pageNo: {}, 校验后pageNo: {}, 原始pageSize: {}, 校验后pageSize: {}", 
                    pageNo, validPageNo, pageSize, validPageSize);
        
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("w", query); // 搜索词（必传）
        additionalParams.put("p", String.valueOf(validPageNo)); // 页码（可选）
        additionalParams.put("num", String.valueOf(validPageSize)); // 每页大小（可选）

        // 歌单搜索需要完整的登录态参数（与其他正常功能保持一致）
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_songlist_search.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("[搜索歌单] 请求URL: {}", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[搜索歌单] 响应状态码: {}", response.statusCode());
            logger.info("[搜索歌单] 响应内容长度: {}", response.body().length());
            logger.debug("[搜索歌单] 响应内容: {}", response.body());
            
            Map<String, Object> result = parseJson(response.body());
            logger.info("[搜索歌单] 解析结果 - ret: {}", result.get("ret"));
            
            return result;
        } catch (Exception e) {
            logger.error("[搜索歌单] 异常", e);
            return new HashMap<>();
        }
    }

    
    /**
     * 获取歌单详情
     */
    public Map<String, Object> getSonglistDetail(String dissid, int page, int pageSize, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("dissid", dissid);
        additionalParams.put("page", String.valueOf(page));
        additionalParams.put("page_size", String.valueOf(pageSize));

        // 关键：添加设备登录态参数
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_get_songlist_detail.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取歌单详情] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return parseJson(response.body());
        } catch (Exception e) {
            logger.error("[获取歌单详情] 异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取歌曲信息
     */
    public Map<String, Object> getSongInfoBatch(List<String> songMids, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        // 将songMids列表转换为逗号分隔的字符串
        String songMidStr = String.join(",", songMids);

        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("song_mid", songMidStr);

        // 关键：添加设备登录态参数
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_get_song_info_batch.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[批量获取歌曲信息] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return parseJson(response.body());
        } catch (Exception e) {
            logger.error("[批量获取歌曲信息] 异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取单首歌曲的播放URL
     * @param songMid 歌曲mid
     * @param openId 用户openId
     * @param accessToken 用户accessToken
     * @param opiDeviceId 设备ID
     * @param opiDeviceKey 设备Key
     * @return 歌曲播放URL（如获取失败返回null）
     */
    public String getSongPlayUrl(String songMid, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        List<String> mids = new ArrayList<>();
        mids.add(songMid);
        Map<String, Object> result = getSongInfoBatch(mids, openId, accessToken, opiDeviceId, opiDeviceKey);
        if (result == null || !result.containsKey("data")) {
            logger.warn("[获取播放URL] 未获取到有效数据");
            return null;
        }
        try {
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            List<Map<String, Object>> songList = (List<Map<String, Object>>) data.get("song_info_list");
            if (songList != null && !songList.isEmpty()) {
                Map<String, Object> songInfo = songList.get(0);
                // 兼容不同字段名
                if (songInfo.containsKey("song_play_url")) {
                    return (String) songInfo.get("song_play_url");
                } else if (songInfo.containsKey("play_url")) {
                    return (String) songInfo.get("play_url");
                }
            }
        } catch (Exception e) {
            logger.error("[获取播放URL] 解析数据异常", e);
        }
        return null;
    }

    /**
     * 批量获取歌曲播放URL
     * @param songMids 歌曲mid列表
     * @param openId 用户openId
     * @param accessToken 用户accessToken
     * @param opiDeviceId 设备ID
     * @param opiDeviceKey 设备Key
     * @return Map<songMid, playUrl>
     */
    public Map<String, String> getSongPlayUrlBatch(List<String> songMids, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, Object> result = getSongInfoBatch(songMids, openId, accessToken, opiDeviceId, opiDeviceKey);
        if (result == null || !result.containsKey("data")) {
            logger.warn("[批量获取播放URL] 未获取到有效数据");
            return resultMap;
        }
        try {
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            List<Map<String, Object>> songList = (List<Map<String, Object>>) data.get("song_info_list");
            if (songList != null) {
                for (Map<String, Object> songInfo : songList) {
                    String mid = (String) songInfo.get("song_mid");
                    String url = null;
                    if (songInfo.containsKey("song_play_url")) {
                        url = (String) songInfo.get("song_play_url");
                    } else if (songInfo.containsKey("play_url")) {
                        url = (String) songInfo.get("play_url");
                    }
                    if (mid != null && url != null) {
                        resultMap.put(mid, url);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("[批量获取播放URL] 解析数据异常", e);
        }
        return resultMap;
    }
    
    /**
     * 获取歌词信息
     */
    public Map<String, Object> getLyric(String songMid, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("song_mid", songMid);
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_get_lyric.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取歌词] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return parseJson(response.body());
        } catch (Exception e) {
            logger.error("[获取歌词] 异常", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取歌手头像信息
     */
    public Map<String, Object> getSingerAvatar(String singerMid, String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("singer_mid", singerMid);
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_get_singer_avatar.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取歌手头像] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return parseJson(response.body());
        } catch (Exception e) {
            logger.error("[获取歌手头像] 异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取电台分类信息
     */
    public Map<String, Object> getRadioCategoryInfo(String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> additionalParams = new HashMap<>();
        
        // 添加设备登录态参数
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_longradio_category_info.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取电台分类信息] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))  // 设置超时时间
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取电台分类信息] 响应状态码: {}", response.statusCode());
            logger.debug("[获取电台分类信息] 响应内容: {}", response.body());
            
            if (response.statusCode() != 200) {
                logger.error("[获取电台分类信息] HTTP错误，状态码: {}", response.statusCode());
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("ret", -1);
                errorResult.put("msg", "HTTP错误，状态码: " + response.statusCode());
                return errorResult;
            }
            
            return parseJson(response.body());
        } catch (Exception e) {
            logger.error("[获取电台分类信息] 请求异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "请求异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取电台分类专辑列表 - 统一的实现
     * 与歌单搜索完全分离，使用正确的接口和参数
     */
    public Map<String, Object> getRadioCategoryAlbumList(int fstId, int sndId, int sortType, int begin, int number,
                                                         String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        // 验证排序类型，确保只使用支持的值：0:最新，1：最热(默认)
        int validSortType = sortType;
        if (sortType != 0 && sortType != 1) {
            validSortType = 1; // 默认使用最热排序
            logger.warn("[警告] 不支持的排序类型: {}，使用默认值: {}", sortType, validSortType);
        }
        
        Map<String, String> additionalParams = new HashMap<>();
        // 使用正确的参数名称
        additionalParams.put("fstId", String.valueOf(fstId));
        additionalParams.put("sndId", String.valueOf(sndId));
        additionalParams.put("sortType", String.valueOf(validSortType));
        additionalParams.put("begin", String.valueOf(begin));
        additionalParams.put("number", String.valueOf(number));
        
        // 添加完整的设备登录态参数（电台分类需要）
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_longradio_category_album_list.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取电台分类专辑列表] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取电台分类专辑列表] 响应状态码: {}", response.statusCode());
            
            if (response.statusCode() != 200) {
                logger.error("[获取电台分类专辑列表] HTTP错误，状态码: {}", response.statusCode());
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("ret", -1);
                errorResult.put("msg", "HTTP错误，状态码: " + response.statusCode());
                return errorResult;
            }
            
            String responseBody = response.body();
            logger.info("[获取电台分类专辑列表] 响应内容长度: {}", responseBody.length());
            
            // 限制日志输出长度，避免日志过大
            if (responseBody.length() > 1000) {
                logger.debug("[获取电台分类专辑列表] 响应内容(前1000字符): {}", responseBody.substring(0, 1000));
            } else {
                logger.debug("[获取电台分类专辑列表] 响应内容: {}", responseBody);
            }
            
            return parseJson(responseBody);
        } catch (Exception e) {
            logger.error("[获取电台分类专辑列表] 请求异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "请求异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 获取电台专辑详情 - 根据接口文档更新
     */
    public Map<String, Object> getRadioAlbumDetail(String albumId, String albumMid, int page, int pageSize, int sortBy, int favState,
                                                   String openId, String accessToken, String opiDeviceId, String opiDeviceKey) {
        Map<String, String> additionalParams = new HashMap<>();
        
        // 根据接口文档，album_id 和 album_mid 二选一，优先使用 album_mid
        if (albumMid != null && !albumMid.isEmpty()) {
            additionalParams.put("album_mid", albumMid);
        } else if (albumId != null && !albumId.isEmpty()) {
            additionalParams.put("album_id", albumId);
        }
        
        // 分页参数：page 从 0 开始，size 最大 50
        int validPage = Math.max(0, page);
        int validSize = Math.max(1, Math.min(pageSize, 50));
        additionalParams.put("page", String.valueOf(validPage));
        additionalParams.put("size", String.valueOf(validSize));
        
        // 验证排序类型，确保只使用支持的值
        int validSortBy = sortBy;
        if (sortBy != -3 && sortBy != 3) {
            validSortBy = -3; // 默认使用正序
            logger.warn("[警告] 不支持的排序类型: {}，使用默认值: {}", sortBy, validSortBy);
        }
        additionalParams.put("sort_by", String.valueOf(validSortBy));
        
        // 收藏状态：1 需要返回用户收藏状态（需登录）
        additionalParams.put("fav_state", String.valueOf(favState));
        
        // 添加设备登录态参数
        additionalParams.put("login_type", "5");
        additionalParams.put("device_login_type", "4");
        additionalParams.put("opi_device_id", opiDeviceId);
        additionalParams.put("opi_device_key", opiDeviceKey);
        additionalParams.put("user_login_type", "6");
        additionalParams.put("qqmusic_open_id", openId);
        additionalParams.put("qqmusic_access_token", accessToken);

        Map<String, String> params = buildCommonParams("fcg_music_custom_get_album_detail.fcg", additionalParams);
        params.put("sign", generateSign(params, APP_KEY));

        String url = BASE_URL + "?" + buildQueryString(params);
        logger.info("\n[获取电台专辑详情] 请求url:\n{}\n", url);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(30))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("[获取电台专辑详情] 响应状态码: {}", response.statusCode());
            
            if (response.statusCode() != 200) {
                logger.error("[获取电台专辑详情] HTTP错误，状态码: {}", response.statusCode());
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("ret", -1);
                errorResult.put("msg", "HTTP错误，状态码: " + response.statusCode());
                return errorResult;
            }
            
            String responseBody = response.body();
            logger.info("[获取电台专辑详情] 响应内容长度: {}", responseBody.length());
            
            // 限制日志输出长度，避免日志过大
            if (responseBody.length() > 1000) {
                logger.debug("[获取电台专辑详情] 响应内容(前1000字符): {}", responseBody.substring(0, 1000));
            } else {
                logger.debug("[获取电台专辑详情] 响应内容: {}", responseBody);
            }
            
            return parseJson(responseBody);
        } catch (Exception e) {
            logger.error("[获取电台专辑详情] 请求异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "请求异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 构建URL查询字符串
     */
    private String buildQueryString(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) {
                sb.append("&");
            }
            sb.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8));
            sb.append("=");
            sb.append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8));
            first = false;
        }
        return sb.toString();
    }

    /**
     * 使用Gson解析JSON字符串为Map
     */
    private Map<String, Object> parseJson(String json) {
        Type mapType = new TypeToken<Map<String, Object>>(){}.getType();
        return gson.fromJson(json, mapType);
    }

    /**
     * 使用Gson将Map转换为JSON字符串
     */
    private String mapToJson(Map<String, String> map) {
        return gson.toJson(map);
    }
}