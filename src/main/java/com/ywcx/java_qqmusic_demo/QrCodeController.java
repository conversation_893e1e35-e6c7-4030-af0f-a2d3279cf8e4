package com.ywcx.java_qqmusic_demo;

import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Controller
public class QrCodeController {

    @GetMapping("/qr-code")
    @ResponseBody
    public ResponseEntity<Resource> getQrCode() throws IOException {
        // 查找二维码文件
        Path qrCodePath = Paths.get("qq_music_login_qr.png");
        
        if (Files.exists(qrCodePath)) {
            InputStreamResource resource = new InputStreamResource(new FileInputStream(qrCodePath.toFile()));
            
            return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_PNG)
                    .body(resource);
        } else {
            // 如果没有二维码文件，返回404
            return ResponseEntity.notFound().build();
        }
    }
}