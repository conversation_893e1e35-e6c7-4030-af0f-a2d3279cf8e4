package com.ywcx.java_qqmusic_demo;

import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;
import java.util.Arrays;

@Service
public class AiChatService {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(AiChatService.class);

    @Value("${moonshot.api.key:}")
    private String moonshotApiKey;

    private final WebSearchService webSearchService;

    private ChatLanguageModel model;
    private StreamingChatLanguageModel streamingModel;

    // 支持依赖注入
    public AiChatService(WebSearchService webSearchService) {
        this.webSearchService = webSearchService;
    }
    // 兼容无参构造（Spring 反射用）
    public AiChatService() {
        this.webSearchService = null;
    }
    
    @PostConstruct
    public void init() {
        // 尝试初始化模型，如果失败则保持为null
        try {
            // 使用Moonshot API（兼容OpenAI格式）
            String apiKey = moonshotApiKey;
            
            if (apiKey == null || apiKey.isEmpty()) {
                apiKey = System.getenv("MOONSHOT_API_KEY");
            }
            
            if (apiKey == null || apiKey.isEmpty()) {
                // 向后兼容OPENAI_API_KEY
                apiKey = System.getenv("OPENAI_API_KEY");
            }
            
            if (apiKey != null && !apiKey.isEmpty()) {
                this.model = OpenAiChatModel.builder()
                        .apiKey(apiKey)
                        .baseUrl("https://api.moonshot.cn/v1")
                        .modelName("kimi-k2-turbo-preview")
                        .build();
                
                this.streamingModel = OpenAiStreamingChatModel.builder()
                        .apiKey(apiKey)
                        .baseUrl("https://api.moonshot.cn/v1")
                        .modelName("kimi-k2-turbo-preview")
                        .build();
                
                log.info("AI模型初始化成功，使用Moonshot API");
            } else {
                log.warn("未检测到API密钥，AI聊天服务将不可用");
            }
        } catch (Exception e) {
            // 如果初始化失败，model保持为null，服务仍然可用
            log.error("AI模型初始化失败", e);
        }
    }
    
    // SYSTEM_PROMPT 增加补充要求，嵌入到原有提示词最后
    private static final String SYSTEM_PROMPT = """
            
            # Role: 儿童教育产品顾问
            
            ## Profile
            - language: 中文
            - description: 一位专注于儿童硬件产品并结合教育学的高级顾问，提供产品开发建议及教育方案。
            - background: 拥有儿童教育领域的丰富经验，兼具产品管理的知识与实践。
            - personality: 具备高度的好奇心和创造力，耐心细致，乐于分享知识。
            - expertise: 儿童教育理论、产品管理、用户研究、项目协调。
            - target_audience: 儿童教育硬件产品开发团队、产品经理、教育工作者。
            
            ## Skills
            
            1. 产品思维
               - 产品战略制定: 擅长结合教育需求制定产品发展战略。
               - 用户体验设计: 能从儿童视角优化产品用户体验。
               - 市场分析: 会分析市场趋势并将其融入产品开发。
               - 产品生命周期管理: 驾驭产品从概念到市场的全流程管理。
            
            2. 教育专家知识
               - 课程设计: 能根据儿童发展阶段设计合适的教育课程。
               - 教学方法: 熟悉各种有效的儿童教学方法。
               - 发展心理学: 理解儿童认知与心理发展特点。
               - 教育科技应用: 将科技工具融入教育实践。
            
            ## Rules
            
            1. 基本原则：
               - 以儿童为中心: 所有产品设计和教育方案均应以儿童发展为核心。
               - 确保安全: 硬件产品必须符合安全标准，无任何安全隐患。
               - 重视反馈: 收集并分析用户反馈，不断优化产品功能。
               - 伦理责任: 维护高标准的职业伦理，不断自我提升。
            
            2. 行为准则：
               - 保持开放沟通: 鼓励团队内的开放讨论和意见交换。
               - 以数据驱动决策: 用可靠的数据指导每次产品迭代。
               - 尊重多样性: 理解并尊重不同文化和背景的需求。
               - 持续学习: 紧跟教育领域和科技发展的最新趋势。
            
            3. 限制条件：
               - 不偏离项目目标: 所有建议和行动必须支持主要项目目标。
               - 无法透露机密信息: 对于项目相关的保密信息需严格保密。
               - 限定预算范围: 产品开发和教育方案应控制在预算范围内。
               - 不接受非科学依据: 建议和决策需基于科学事实和研究。
            
            ## Workflows
            
            - 目标: 开发结合教育实用性的高质量儿童硬件产品。
            - 步骤 1: 进行市场和用户需求研究，明确产品定位。
            - 步骤 2: 基于教育理论和用户需求进行产品功能设计。
            - 步骤 3: 协调产品开发和测试阶段，确保产品符合教育和安全标准。
            - 预期结果: 成功推出符合市场需求，具有创新教育功能的儿童硬件产品。
            
            ## Initialization
            作为儿童教育产品顾问，你必须遵守上述Rules，按照Workflows执行任务。
            
            ## PromptDefinitions
            如果故事难度同时适合两个年龄段，你可以为这些数据打分，但是不要输出出来，最后确定一个年龄区间。

            你必须按照如下 JSON 格式输出你的推理结果：
            {"age":"3 - 4","content":""}
            其中“age”就是你分析出来的具体年龄，具体到某一岁，如 3 - 4、4 - 5、5 - 6 这样；
            “content”就是你的推断内容。
            只允许输出上述 JSON 格式，禁止输出多余内容。
            必须按照 json 格式输出，其中“age”就是你分析出来的具体年龄，具体到某一岁，如 3 - 4、4 - 5、5 - 6 这样；“content”就是你的推断内容，例如:{"age":"3 - 4","content":""}
            """;

    private static final String SYS_PRO = """
            你是晨星儿童教育垂类大模型，由仰望晨星团队训练开发，专注儿童教育领域。
            
            ## 核心能力
            - 儿童认知发展分析与指导
            - 教育内容创作与策划
            - 儿童行为心理分析
            - 教育产品设计与优化
            - 儿童内容的识别（如根据内容定位到具体的年龄）
            
            ## 回答风格
            尽可能的丰富，回答问题，并阐述出回答的思路，和判断的逻辑，请从多个维度来分析。
            """;

    public String chat(String userMessage) {
        if (model == null) {
            return "AI聊天服务暂不可用，请检查MOONSHOT_API_KEY环境变量是否设置";
        }

        if (userMessage == null || userMessage.trim().isEmpty()) {
            return "请输入有效的问题";
        }

        try {
            // 联网搜索增强
            String webResult = "";
            if (webSearchService != null) {
                webResult = webSearchService.searchWeb(userMessage);
            }
            String combinedPrompt = (webResult.isEmpty() ? "" : (webResult + "\n\n")) + SYS_PRO + "\n\n" + SYSTEM_PROMPT;
            Response<AiMessage> response = model.generate(
                new SystemMessage(combinedPrompt),
                new UserMessage(userMessage)
            );
            String aiText = (response != null && response.content() != null) ? response.content().text() : null;

            // 记录大模型原始返回日志
            log.info("[AI对话][同步][原始JSON] 用户输入: {}\nAI原始回复: {}", userMessage, aiText);

            // 解析 JSON 并 switch 判断 age 区间（每个分支都直接返回原始数据）
            if (aiText != null) {
                try {
                    org.json.JSONObject obj = new org.json.JSONObject(aiText);
                    String age = obj.optString("age", "");
                    // 这里 switch 只是示例，所有分支都直接返回原始 aiText
                    switch (age) {
                        case "3 - 4": {
                            // case 3-4 使用PromptDefinitions中的分类提示词进行AI请求
                            try {
                                Response<AiMessage> newResponse = model.generate(
                                    new SystemMessage(PromptDefinitions.CATEGORY_SELECTION_PROMPT),
                                    new UserMessage(userMessage)
                                );
                                String newAiText = (newResponse != null && newResponse.content() != null) ? newResponse.content().text() : null;
                                log.info("[AI对话][case 3-4 分类请求] 用户输入: {}\nAI新回复: {}", userMessage, newAiText);
                                if (newAiText != null) {
                                    try {
                                        // 组合返回aiText和newAiText的结果
                                        try {
                                            // 解析原始aiText的JSON内容
                                            org.json.JSONObject originalObj = new org.json.JSONObject(aiText);
                                            String originalContent = originalObj.optString("content", "");
                                            
                                            // 创建组合结果
                                            org.json.JSONObject combinedObj = new org.json.JSONObject();
                                            combinedObj.put("age", "3 - 4");
                                            combinedObj.put("original_analysis", originalContent);
                                            combinedObj.put("category_selection", newAiText);
                                            
                                            return combinedObj.toString();
                                        } catch (Exception jsonEx) {
                                            // 如果解析失败，直接拼接
                                            return String.format("{\"age\":\"3 - 4\",\"original_analysis\":\"%s\",\"category_selection\":\"%s\"}", 
                                                aiText.replace("\"", "\\\""), 
                                                newAiText.replace("\"", "\\\""));
                                        }
                                    } catch (Exception e) {
                                        log.error("case 3-4 分类请求处理失败", e);
                                        return newAiText;
                                    }
                                } else {
                                    return aiText;
                                }
                            } catch (Exception e) {
                                log.error("case 3-4 分类请求失败，返回原始aiText: {}\n异常: ", aiText, e);
                                return aiText;
                            }
                        }
                        case "4 - 5": {
                            // case 4-5 使用PromptDefinitions中的4-5岁分类提示词进行AI请求
                            try {
                                Response<AiMessage> newResponse = model.generate(
                                    new SystemMessage(PromptDefinitions.CATEGORY_SELECTION_PROMPT_4_5),
                                    new UserMessage(userMessage)
                                );
                                String newAiText = (newResponse != null && newResponse.content() != null) ? newResponse.content().text() : null;
                                log.info("[AI对话][case 4-5 分类请求] 用户输入: {}\nAI新回复: {}", userMessage, newAiText);
                                if (newAiText != null) {
                                    try {
                                        // 解析原始aiText的JSON内容
                                        org.json.JSONObject originalObj = new org.json.JSONObject(aiText);
                                        String originalContent = originalObj.optString("content", "");
                                        
                                        // 创建组合结果
                                        org.json.JSONObject combinedObj = new org.json.JSONObject();
                                        combinedObj.put("age", "4 - 5");
                                        combinedObj.put("original_analysis", originalContent);
                                        combinedObj.put("category_selection", newAiText);
                                        
                                        return combinedObj.toString();
                                    } catch (Exception jsonEx) {
                                        // 如果解析失败，直接拼接
                                        return String.format("{\"age\":\"4 - 5\",\"original_analysis\":\"%s\",\"category_selection\":\"%s\"}", 
                                            aiText.replace("\"", "\\\""), 
                                            newAiText.replace("\"", "\\\""));
                                    }
                                } else {
                                    return aiText;
                                }
                            } catch (Exception e) {
                                log.error("case 4-5 分类请求失败，返回原始aiText: {}\n异常: ", aiText, e);
                                return aiText;
                            }
                        }
                        case "5 - 6": {
                            // case 5-6 使用PromptDefinitions中的5-6岁分类提示词进行AI请求
                            try {
                                Response<AiMessage> newResponse = model.generate(
                                    new SystemMessage(PromptDefinitions.CATEGORY_SELECTION_PROMPT_5_6),
                                    new UserMessage(userMessage)
                                );
                                String newAiText = (newResponse != null && newResponse.content() != null) ? newResponse.content().text() : null;
                                log.info("[AI对话][case 5-6 分类请求] 用户输入: {}\nAI新回复: {}", userMessage, newAiText);
                                if (newAiText != null) {
                                    try {
                                        // 解析原始aiText的JSON内容
                                        org.json.JSONObject originalObj = new org.json.JSONObject(aiText);
                                        String originalContent = originalObj.optString("content", "");
                                        
                                        // 创建组合结果
                                        org.json.JSONObject combinedObj = new org.json.JSONObject();
                                        combinedObj.put("age", "5 - 6");
                                        combinedObj.put("original_analysis", originalContent);
                                        combinedObj.put("category_selection", newAiText);
                                        
                                        return combinedObj.toString();
                                    } catch (Exception jsonEx) {
                                        // 如果解析失败，直接拼接
                                        return String.format("{\"age\":\"5 - 6\",\"original_analysis\":\"%s\",\"category_selection\":\"%s\"}", 
                                            aiText.replace("\"", "\\\""), 
                                            newAiText.replace("\"", "\\\""));
                                    }
                                } else {
                                    return aiText;
                                }
                            } catch (Exception e) {
                                log.error("case 5-6 分类请求失败，返回原始aiText: {}\n异常: ", aiText, e);
                                return aiText;
                            }
                        }
                        default: {
                            // 对于其他年龄段，直接返回原始分析结果
                            return aiText;
                        }
                    }
                } catch (Exception jsonEx) {
                    log.error("AI返回内容非标准JSON: {}\n异常: ", aiText, jsonEx);
                    return aiText;
                }
            } else {
                return "AI暂时无法回答您的问题，请稍后再试";
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                errorMsg = "AI服务连接失败，请检查网络连接或API密钥配置";
            } else if (errorMsg.contains("API key")) {
                errorMsg = "API密钥配置错误，请联系管理员";
            } else if (errorMsg.contains("timeout") || errorMsg.contains("Timeout")) {
                errorMsg = "AI服务响应超时，请稍后重试";
            } else if (errorMsg.contains("connection") || errorMsg.contains("Connection")) {
                errorMsg = "无法连接到AI服务，请检查网络连接";
            }
            log.error("AI对话出错: {}\n异常: ", errorMsg, e);
            return "AI分析失败: " + errorMsg;
        }
    }

    public void chatStream(String userMessage, java.util.function.Consumer<String> onPartialResponse, java.lang.Runnable onComplete) {
        if (streamingModel == null) {
            onPartialResponse.accept("AI聊天服务暂不可用，请检查MOONSHOT_API_KEY环境变量是否设置");
            onComplete.run();
            return;
        }

        if (userMessage == null || userMessage.trim().isEmpty()) {
            onPartialResponse.accept("请输入有效的问题");
            onComplete.run();
            return;
        }

        try {
            // 联网搜索增强
            final String webResult;
            if (webSearchService != null) {
                String tmp = webSearchService.searchWeb(userMessage);
                if (tmp != null && !tmp.isEmpty()) {
                    onPartialResponse.accept(tmp + "\n\n");
                }
                webResult = tmp == null ? "" : tmp;
            } else {
                webResult = "";
            }
            final String combinedPrompt = (webResult.isEmpty() ? "" : (webResult + "\n\n")) + SYS_PRO + "\n\n" + SYSTEM_PROMPT;

            // 使用正确的消息列表格式进行流式处理
            streamingModel.generate(
                Arrays.asList(
                    new SystemMessage(combinedPrompt),
                    new UserMessage(userMessage)
                ),
                new StreamingResponseHandler<AiMessage>() {
                    private final StringBuilder fullResponse = new StringBuilder();

                    @Override
                    public void onNext(String token) {
                        if (token != null && !token.isEmpty()) {
                            fullResponse.append(token);
                            onPartialResponse.accept(token);
                        }
                    }

                    @Override
                    public void onComplete(Response<AiMessage> response) {
                        log.info("[AI对话][流式] 用户输入: {} | 联网摘要: {} | Prompt: {} | AI回复: {}", userMessage, webResult, combinedPrompt, fullResponse.toString());
                        onComplete.run();
                    }

                    @Override
                    public void onError(Throwable error) {
                        log.error("AI流式对话出错", error);
                        onPartialResponse.accept("\n\n抱歉，AI对话服务暂时不可用，请稍后再试。");
                        onComplete.run();
                    }
                }
            );

        } catch (Exception e) {
            log.error("AI流式对话初始化出错", e);
            onPartialResponse.accept("AI对话服务暂时不可用，请稍后再试");
            onComplete.run();
        }
    }
}
