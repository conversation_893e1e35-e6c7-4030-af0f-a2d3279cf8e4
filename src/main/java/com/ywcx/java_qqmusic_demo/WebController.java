package com.ywcx.java_qqmusic_demo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller

public class WebController {
    private static final Logger logger = LoggerFactory.getLogger(WebController.class);

    // 用于缓存上传的文件内容，token -> fileContent
    private static final java.util.concurrent.ConcurrentHashMap<String, String> uploadFileCache = new java.util.concurrent.ConcurrentHashMap<>();

    private final QQMusicService qqMusicService;
    private final ChildrenRadioCrawlerService crawlerService;
    private final AiChatService aiChatService;

    public WebController(QQMusicService qqMusicService, ChildrenRadioCrawlerService crawlerService, AiChatService aiChatService) {
        this.qqMusicService = qqMusicService;
        this.crawlerService = crawlerService;
        this.aiChatService = aiChatService;
    }

    // 首页，显示二维码登录
    @GetMapping("/")
    public String index(Model model) {
        logger.info("[首页] 访问首页");
        String qrCodeUrl = qqMusicService.getQrCodeUrl();
        model.addAttribute("qrCodeUrl", qrCodeUrl);
        return "index";
    }

    // 检查登录状态
    @GetMapping("/check-login")
    @ResponseBody
    public Map<String, Object> checkLogin() {
        boolean isLoggedIn = qqMusicService.isLoggedIn();
        Map<String, Object> result = new HashMap<>();
        result.put("loggedIn", isLoggedIn);
        return result;
    }

    // 电台分类页
    @GetMapping("/radio")
    public String radioCategories(Model model) {
        Map<String, Object> categoryResult = qqMusicService.getRadioCategoryInfo();
        model.addAttribute("categoryResult", categoryResult);
        return "radio-categories";
    }

    // 电台专辑列表页
    @GetMapping("/radio/albums")
    public String radioAlbumsPage(
            @RequestParam("fstId") Long fstId,
            @RequestParam("sndId") Long sndId,
            @RequestParam(value = "sortType", defaultValue = "0") Integer sortType,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "title", required = false) String title,
            Model model) {
        int pageSize = 20;
        int begin = page * pageSize;
        Map<String, Object> result = qqMusicService.getRadioCategoryAlbumList(
                fstId.intValue(), sndId.intValue(), sortType != null ? sortType : 0, begin, pageSize);
        model.addAttribute("albumLists", result.get("albumLists"));
        model.addAttribute("total", result.get("total"));
        model.addAttribute("fstId", fstId);
        model.addAttribute("sndId", sndId);
        model.addAttribute("sortType", sortType != null ? sortType : 0);
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", pageSize);
        model.addAttribute("categoryTitle", title != null ? title : "电台分类");
        return "radio-albums";
    }

    // 电台专辑详情页
    @GetMapping("/radio/album/{albumId}")
    public String getRadioAlbumDetail(@PathVariable String albumId,
                                      @RequestParam(value = "albumMid", required = false) String albumMid,
                                      @RequestParam(value = "page", defaultValue = "0") int page,
                                      @RequestParam(value = "sortBy", defaultValue = "-3") int sortBy,
                                      @RequestParam(value = "favState", defaultValue = "1") int favState,
                                      Model model) {
        int pageSize = 50;
        Map<String, Object> result = qqMusicService.getRadioAlbumDetail(albumId, albumMid, page, pageSize, sortBy, favState);
        model.addAttribute("result", result);
        model.addAttribute("albumId", albumId);
        model.addAttribute("albumMid", albumMid);
        model.addAttribute("page", page);
        model.addAttribute("sortBy", sortBy);
        model.addAttribute("favState", favState);
        return "radio-album-detail";
    }

    // 搜索页 GET
    @GetMapping("/search")
    public String searchPage(Model model) {
        return "search";
    }

    // 搜索页 POST
    @PostMapping("/search")
    public String search(
            @RequestParam("query") String query,
            @RequestParam(value = "type", defaultValue = "0") int type,
            @RequestParam(value = "page", defaultValue = "1") int page,
            Model model) {
        int pageSize = 20;
        Map<String, Object> results;
        if (type == 100) {
            int songlistPageSize = Math.min(pageSize, 10);
            int songlistPage = Math.min(page, 2);
            if (songlistPage == 0) songlistPage = 1;
            results = qqMusicService.searchSonglist(query, songlistPage, songlistPageSize);
        } else {
            results = qqMusicService.searchMusic(query, type, page, pageSize);
        }
        model.addAttribute("query", query);
        model.addAttribute("type", type);
        model.addAttribute("page", page);
        model.addAttribute("results", results);
        return "search";
    }

    // 歌单详情页
    @GetMapping("/songlist/{dissid}")
    public String getSonglistDetail(@PathVariable("dissid") String dissid,
                                    @RequestParam(value = "page", defaultValue = "0") int page,
                                    Model model) {
        int pageSize = 30;
        Map<String, Object> result = qqMusicService.getSonglistDetail(dissid, page, pageSize);
        model.addAttribute("result", result);
        model.addAttribute("dissid", dissid);
        model.addAttribute("page", page);
        return "songlist-detail";
    }
    
    // 获取歌曲详情
    @PostMapping("/song-detail")
    @ResponseBody
    public Map<String, Object> getSongDetail(@RequestParam("song_mid") String songMid,
                                             @RequestParam("singer_mid") String singerMid) {
        logger.info("[获取歌曲详情] 入参 - songMid: {}, singerMid: {}", songMid, singerMid);
        
        Map<String, Object> result = new HashMap<>();
        
        if (!qqMusicService.isLoggedIn()) {
            logger.warn("[获取歌曲详情] 用户未登录");
            result.put("ret", -1);
            result.put("msg", "用户未登录");
            return result;
        }
        
        try {
            // 获取歌曲基本信息
            List<String> songMids = new ArrayList<>();
            songMids.add(songMid);
            Map<String, Object> songInfo = qqMusicService.getSongInfoBatch(songMids);
            result.put("song_info", songInfo);
            
            // 获取歌手头像
            if (singerMid != null && !singerMid.isEmpty()) {
                Map<String, Object> avatarInfo = qqMusicService.getSingerAvatar(singerMid);
                result.put("avatar", avatarInfo);
            }
            
            // 获取歌词
            Map<String, Object> lyricInfo = qqMusicService.getLyric(songMid);
            result.put("lyric", lyricInfo);
            
            logger.info("[获取歌曲详情] 获取成功");
            return result;
        } catch (Exception e) {
            logger.error("[获取歌曲详情] 异常", e);
            result.put("ret", -1);
            result.put("msg", "获取歌曲详情失败: " + e.getMessage());
            return result;
        }
    }
    
    // 导出儿歌电台最热排行Top100为Excel文件
    @GetMapping("/radio/children-crawler/export")
    public ResponseEntity<Resource> exportChildrenRadioTop100() throws IOException {
        List<ChildrenRadioCrawlerService.RadioSongInfo> songs = crawlerService.crawlChildrenRadioTop100();
        byte[] excelData = crawlerService.exportToExcel(songs);
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelData));
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=儿歌电台最热排行Top100.xlsx");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(excelData.length)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    // 按细分类目导出最热Top100歌单为Excel
    @GetMapping("/radio/children-crawler/export-by-category")
    public ResponseEntity<Resource> exportChildrenRadioTop100ByCategory(
            @RequestParam("fstId") Integer fstId,
            @RequestParam("sndId") Integer sndId,
            @RequestParam("categoryName") String categoryName) throws IOException {
        try {
            List<ChildrenRadioCrawlerService.RadioSongInfo> songs =
                    crawlerService.getTop100SongsByCategory(fstId, sndId, categoryName);
            byte[] excelData = crawlerService.exportToExcel(songs);
            InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelData));
            
            String filename = java.net.URLEncoder.encode(categoryName + "_最热Top100.xlsx", "UTF-8");
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + filename);
            headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(excelData.length)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);
        } catch (Exception e) {
            logger.error("[按分类导出] 导出失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    // 导出儿歌电台最热排行Top100为Excel文件（包含歌单详细信息）
    @GetMapping("/radio/children-crawler/export-with-songlist-info")
    public ResponseEntity<Resource> exportChildrenRadioTop100WithSonglistInfo() throws IOException {
        logger.info("[导出歌单详细信息] 开始导出包含歌单详细信息的Excel文件");
        
        try {
            List<ChildrenRadioCrawlerService.RadioSongInfo> songs = crawlerService.crawlChildrenRadioTop100();
            logger.info("[导出歌单详细信息] 获取到 {} 首歌曲", songs.size());
            
            byte[] excelData = crawlerService.exportToExcel(songs);
            logger.info("[导出歌单详细信息] Excel数据生成完成，大小: {} bytes", excelData.length);
            
            InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelData));
            
            // 设置正确的文件名（使用URL编码避免中文问题）
            String filename = java.net.URLEncoder.encode("儿歌电台最热排行Top100_含歌单详情.xlsx", "UTF-8");
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + filename);
            headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(excelData.length)
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("[导出歌单详细信息] 导出失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    // 按细分类目导出最热Top100歌单为Excel（包含歌单详细信息）
    @GetMapping("/radio/children-crawler/export-by-category-with-songlist-info")
    public ResponseEntity<Resource> exportChildrenRadioTop100ByCategoryWithSonglistInfo(
            @RequestParam("fstId") Integer fstId,
            @RequestParam("sndId") Integer sndId,
            @RequestParam("categoryName") String categoryName) throws IOException {
        logger.info("[导出分类歌单详细信息] 开始导出分类 {} 的包含歌单详细信息的Excel文件", categoryName);
        
        List<ChildrenRadioCrawlerService.RadioSongInfo> songs =
                crawlerService.getTop100SongsByCategory(fstId, sndId, categoryName);
        byte[] excelData = crawlerService.exportToExcel(songs);
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelData));
        String filename = categoryName + "_最热Top100_含歌单详情.xlsx";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(excelData.length)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    // AI对话接口 - 优化版本
    @PostMapping("/api/chat")
    @ResponseBody
    public Map<String, Object> chat(@RequestBody Map<String, String> requestBody) {
        String message = requestBody.get("message");
        logger.info("[AI对话] 用户消息: {}", message);
        
        Map<String, Object> result = new HashMap<>();
        String aiResponse = aiChatService.chat(message);
        result.put("response", aiResponse);
        return result;
    }

    // 文件上传接口
    @PostMapping("/api/upload")
    @ResponseBody
    public Map<String, Object> handleFileUpload(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
    
        logger.info("收到文件上传请求，文件名: {}", file.getOriginalFilename());
        logger.info("文件大小: {} bytes", file.getSize());
    
        if (file.isEmpty()) {
            logger.warn("上传文件为空");
            result.put("responses", List.of("上传文件为空"));
            return result;
        }
    
        // 验证文件类型
        String fileName = file.getOriginalFilename();
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            boolean isValidType = lowerFileName.endsWith(".txt") ||
                    lowerFileName.endsWith(".xls") ||
                    lowerFileName.endsWith(".xlsx") ||
                    lowerFileName.endsWith(".doc") ||
                    lowerFileName.endsWith(".docx") ||
                    lowerFileName.endsWith(".pdf");
    
            if (!isValidType) {
                logger.warn("不支持的文件类型: {}", fileName);
                result.put("responses", List.of("不支持的文件类型。请上传 .txt, .xls, .xlsx, .doc, .docx 或 .pdf 文件。"));
                return result;
            }
        }
    
        // 验证文件大小 (最大10MB)
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxSize) {
            logger.warn("文件过大: {} bytes", file.getSize());
            result.put("responses", List.of("文件过大，请上传小于10MB的文件。"));
            return result;
        }
    
        try {
            // 解析文件内容
            String fileContent = parseFileContent(file);
        
            // 打印文件内容到日志
            logger.info("上传文件内容预览（前500字符）: {}",
                    fileContent.length() > 500 ? fileContent.substring(0, 500) + "..." : fileContent);
            logger.debug("完整上传文件内容: {}", fileContent);
        
            // 调试：输出分割符和分割后段落数
            String debugContent = fileContent.replace("\n", "\\n").replace("\r", "\\r");
            logger.info("分割前内容（特殊字符可见）：{}", debugContent);
        
            // 尝试用正则分割，兼容换行、空格等
            String[] stories = fileContent.split("\\s*YWCX\\s*");
            logger.info("分割后故事数量: {}", stories.length);
        
            List<Map<String, String>> responseList = new java.util.ArrayList<>();
            for (int i = 0; i < stories.length; i++) {
                String story = stories[i].trim();
                if (!story.isEmpty()) {
                    logger.info("第{}段故事内容（前100字）：{}", i + 1, story.length() > 100 ? story.substring(0, 100) + "..." : story);
                    try {
                        String aiResp = aiChatService.chat(story);
                        logger.info("第{}段AI返回内容：{}", i + 1, aiResp);
                        Map<String, String> item = new HashMap<>();
                        item.put("story", story);
                        item.put("ai", aiResp);
                        responseList.add(item);
                    } catch (Exception aiEx) {
                        String errorMsg = aiEx.getMessage();
                        if (errorMsg == null || errorMsg.trim().isEmpty()) {
                            errorMsg = "AI服务暂时不可用，请检查API密钥配置或稍后重试";
                        }
                        logger.error("AI分析失败 - 故事片段{}: {}\n异常: ", i + 1, errorMsg, aiEx);
                        Map<String, String> item = new HashMap<>();
                        item.put("story", story);
                        item.put("ai", "AI分析失败: " + errorMsg);
                        responseList.add(item);
                    }
                } else {
                    logger.warn("第{}段为空，已跳过", i + 1);
                }
            }
            if (responseList.isEmpty()) {
                Map<String, String> item = new HashMap<>();
                item.put("story", "");
                item.put("ai", "未检测到有效的故事内容，请检查文档格式。");
                responseList.add(item);
            }
            result.put("responses", responseList);
        } catch (Exception e) {
            logger.error("文件上传处理失败", e);
            Map<String, String> item = new HashMap<>();
            item.put("story", "");
            item.put("ai", "文件处理失败: " + e.getMessage());
            result.put("responses", List.of(item));
        }
    
        return result;
    }
    
        /**
         * SSE流式上传+AI分析接口
         * 上传文档后，分割内容，逐段调用AI（严格复用chat方法），每段结果实时推送
         */
        /**
         * 步骤1：上传文件，缓存内容，返回token
         */
        @PostMapping(value = "/api/upload/stream", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
        @ResponseBody
        public Map<String, Object> handleFileUploadStreamV2(@RequestParam("file") MultipartFile file) {
            Map<String, Object> result = new HashMap<>();
            try {
                if (file.isEmpty()) {
                    result.put("status", "error");
                    result.put("msg", "上传文件为空");
                    return result;
                }
                // 验证文件类型
                String fileName = file.getOriginalFilename();
                if (fileName != null) {
                    String lowerFileName = fileName.toLowerCase();
                    boolean isValidType = lowerFileName.endsWith(".txt") ||
                            lowerFileName.endsWith(".xls") ||
                            lowerFileName.endsWith(".xlsx") ||
                            lowerFileName.endsWith(".doc") ||
                            lowerFileName.endsWith(".docx") ||
                            lowerFileName.endsWith(".pdf");
                    if (!isValidType) {
                        result.put("status", "error");
                        result.put("msg", "不支持的文件类型。请上传 .txt, .xls, .xlsx, .doc, .docx 或 .pdf 文件。");
                        return result;
                    }
                }
                // 验证文件大小 (最大10MB)
                long maxSize = 10 * 1024 * 1024; // 10MB
                if (file.getSize() > maxSize) {
                    result.put("status", "error");
                    result.put("msg", "文件过大，请上传小于10MB的文件。");
                    return result;
                }
                // 解析文件内容
                String fileContent = parseFileContent(file);
                // 生成唯一token
                String token = java.util.UUID.randomUUID().toString();
                // 缓存内容
                uploadFileCache.put(token, fileContent);
                result.put("status", "ok");
                result.put("token", token);
                return result;
            } catch (Exception e) {
                result.put("status", "error");
                result.put("msg", "文件处理失败: " + e.getMessage());
                return result;
            }
        }
    
        /**
         * 工具方法：将字符串安全转为JSON字符串（带引号并转义）
         */
        private String toJsonString(String s) {
            if (s == null) return "\"\"";
            return "\"" + s.replace("\\", "\\\\").replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r") + "\"";
        }

    /**
     * 解析上传文件的内容
     * @param file 上传的文件
     * @return 文件内容字符串
     * @throws IOException IO异常
     */
    private String parseFileContent(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        String fileContent = "";

        if (fileName != null) {
            if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
                // 使用Apache POI解析Excel文件
                fileContent = parseExcelFile(file);
            } else if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
                // 使用Apache POI解析Word文件
                fileContent = parseWordFile(file);
            } else {
                // 其他文本文件直接读取内容
                fileContent = new String(file.getBytes(), "UTF-8");
            }
        }

        // 限制内容长度，避免过大
        /*
        // 原有内容截断逻辑，已注释，避免分割符丢失导致只分一段
        if (fileContent.length() > 2000) {
            fileContent = fileContent.substring(0, 2000) + "...(内容已截断)";
        }
        */

        return fileContent;
    }

    /**
     * 解析Excel文件内容
     * @param file Excel文件
     * @return 解析后的内容
     * @throws IOException IO异常
     */
    private String parseExcelFile(MultipartFile file) throws IOException {
        StringBuilder content = new StringBuilder();
        content.append("Excel文件内容:\n");

        try (var workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(file.getInputStream())) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                var sheet = workbook.getSheetAt(i);
                content.append("Sheet ").append(i + 1).append(" (").append(sheet.getSheetName()).append("):\n");
                
                for (org.apache.poi.ss.usermodel.Row row : sheet) {
                    content.append("Row ").append(row.getRowNum() + 1).append(": ");
                    for (org.apache.poi.ss.usermodel.Cell cell : row) {
                        switch (cell.getCellType()) {
                            case STRING:
                                content.append(cell.getStringCellValue()).append("\t");
                                break;
                            case NUMERIC:
                                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                                    content.append(cell.getDateCellValue()).append("\t");
                                } else {
                                    content.append(cell.getNumericCellValue()).append("\t");
                                }
                                break;
                            case BOOLEAN:
                                content.append(cell.getBooleanCellValue()).append("\t");
                                break;
                            case FORMULA:
                                content.append(cell.getCellFormula()).append("\t");
                                break;
                            default:
                                content.append(" \t");
                        }
                    }
                    content.append("\n");
                }
                content.append("\n");
            }
        } catch (Exception e) {
            content.append("解析Excel文件时出错: ").append(e.getMessage());
        }

        return content.toString();
    }

    /**
     * 解析Word文件内容
     * @param file Word文件
     * @return 解析后的内容
     * @throws IOException IO异常
     */
    private String parseWordFile(MultipartFile file) throws IOException {
        StringBuilder content = new StringBuilder();
        content.append("Word文件内容:\n");

        try (var document = new org.apache.poi.xwpf.usermodel.XWPFDocument(file.getInputStream())) {
            for (var paragraph : document.getParagraphs()) {
                content.append(paragraph.getText()).append("\n");
            }
            
            // 解析表格内容
            for (var table : document.getTables()) {
                content.append("\n表格内容:\n");
                for (var row : table.getRows()) {
                    for (var cell : row.getTableCells()) {
                        content.append(cell.getText()).append("\t");
                    }
                    content.append("\n");
                }
            }
        } catch (Exception e) {
            content.append("解析Word文件时出错: ").append(e.getMessage());
        }

        return content.toString();
    }

    // AI对话流式接口 - 优化版本
    @RequestMapping(value = "/api/chat/stream", method = {RequestMethod.GET, RequestMethod.POST})
    public SseEmitter chatStream(
            @RequestBody(required = false) Map<String, String> requestBody,
            @RequestParam(value = "message", required = false) String messageParam) {
        // 支持GET和POST两种方式获取消息
        String actualMessage = null;
        if (requestBody != null && requestBody.get("message") != null) {
            actualMessage = requestBody.get("message");
        } else if (messageParam != null) {
            actualMessage = messageParam;
        }
        
        if (actualMessage == null) {
            SseEmitter emitter = new SseEmitter(300000L);
            try {
                SseEmitter.SseEventBuilder errorEvent = SseEmitter.event()
                    .data("消息内容不能为空")
                    .name("error");
                emitter.send(errorEvent);
                emitter.complete();
            } catch (IOException e) {
                logger.error("发送错误事件失败", e);
                emitter.complete();
            }
            return emitter;
        }
        
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        
        // 设置超时和错误处理
        emitter.onTimeout(() -> {
            logger.warn("SSE连接超时");
            emitter.complete();
        });
        
        emitter.onError((throwable) -> {
            logger.error("SSE连接出错", throwable);
            emitter.complete();
        });
        
        // 创建final变量供lambda表达式使用
        final String finalMessage = actualMessage;
        
        // 异步处理流式响应
        new Thread(() -> {
            try {
                logger.info("开始流式AI对话 | 消息: {}", finalMessage);
                
                aiChatService.chatStream(finalMessage, 
                    partialResponse -> {
                        try {
                            if (partialResponse != null && !partialResponse.isEmpty()) {
                                SseEmitter.SseEventBuilder event = SseEmitter.event()
                                    .data(partialResponse)
                                    .id(String.valueOf(System.currentTimeMillis()))
                                    .name("message");
                                emitter.send(event);
                            }
                        } catch (IOException e) {
                            logger.error("发送流式数据失败", e);
                            emitter.completeWithError(e);
                        } catch (IllegalStateException e) {
                            logger.warn("SSE连接已关闭，停止发送数据");
                        }
                    },
                    () -> {
                        try {
                            // 发送完成信号
                            SseEmitter.SseEventBuilder doneEvent = SseEmitter.event()
                                .data("")
                                .name("done");
                            emitter.send(doneEvent);
                            logger.info("流式AI对话完成");
                        } catch (IOException e) {
                            logger.error("发送完成信号失败", e);
                        } catch (IllegalStateException e) {
                            logger.warn("SSE连接已关闭，无法发送完成信号");
                        } finally {
                            emitter.complete();
                        }
                    }
                );
                
            } catch (Exception e) {
                logger.error("流式对话出错", e);
                try {
                    SseEmitter.SseEventBuilder errorEvent = SseEmitter.event()
                        .data("AI对话服务暂时不可用，请稍后再试")
                        .name("error");
                    emitter.send(errorEvent);
                } catch (IOException ioException) {
                    logger.error("发送错误事件失败", ioException);
                } catch (IllegalStateException stateException) {
                    logger.warn("SSE连接已关闭，无法发送错误事件");
                } finally {
                    emitter.complete();
                }
            }
        }).start();
        
        return emitter;
    }

    // 专业AI对话页面
    @GetMapping("/ai-chat")
    public String aiChatPage(Model model) {
        return "ai-chat";
    }

    // 工具方法
    private Object safeGetRetValue(Map<String, Object> result) {
        try {
            return result.get("ret");
        } catch (Exception e) {
            return "无法获取";
        }
    }

    /**
     * 步骤2：GET SSE，读取缓存内容并推送
     */
    @GetMapping("/api/upload/stream")
    public SseEmitter handleFileUploadStreamSse(@RequestParam("token") String token) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        new Thread(() -> {
            try {
                String fileContent = uploadFileCache.get(token);
                if (fileContent == null) {
                    SseEmitter.SseEventBuilder event = SseEmitter.event()
                        .data("{\"status\":\"error\",\"msg\":\"未找到上传内容或token已过期\"}")
                        .name("error");
                    emitter.send(event);
                    emitter.complete();
                    return;
                }
                // 分割内容
                String[] stories = fileContent.split("YWCX");
                int total = stories.length;
                logger.info("SSE流式AI分析 | 共{}段", total);
                
                // 首先发送总段数信息
                SseEmitter.SseEventBuilder countEvent = SseEmitter.event()
                    .data(String.format("{\"status\":\"count\",\"total\":%d}", total))
                    .name("message");
                emitter.send(countEvent);
                
                // 逐段处理并推送
                for (int i = 0; i < total; i++) {
                    String story = stories[i].trim();
                    if (!story.isEmpty()) {
                        try {
                            // 发送开始处理当前段的信号
                            SseEmitter.SseEventBuilder startEvent = SseEmitter.event()
                                .data(String.format("{\"status\":\"start\",\"index\":%d,\"story\":%s}", i, toJsonString(story)))
                                .name("message");
                            emitter.send(startEvent);
                            
                            // 调用AI服务
                            String aiResp = aiChatService.chat(story);
                            
                            // 发送AI分析结果
                            SseEmitter.SseEventBuilder resultEvent = SseEmitter.event()
                                .data(String.format("{\"status\":\"result\",\"index\":%d,\"ai\":%s}", i, toJsonString(aiResp)))
                                .name("message");
                            emitter.send(resultEvent);
                            
                            // 短暂延迟，让前端能够清晰看到每个段的处理过程
                            Thread.sleep(500);
                        } catch (Exception aiEx) {
                            String errorMsg = aiEx.getMessage();
                            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                                errorMsg = "AI服务暂时不可用，请检查API密钥配置或稍后重试";
                            }
                            logger.error("SSE流式AI分析失败 - 故事片段{}: {}\n异常: ", i + 1, errorMsg, aiEx);
                            SseEmitter.SseEventBuilder errorEvent = SseEmitter.event()
                                .data(String.format("{\"status\":\"error\",\"index\":%d,\"msg\":\"AI分析失败: %s\"}", i, toJsonString(errorMsg)))
                                .name("error");
                            emitter.send(errorEvent);
                        }
                    }
                }
                
                // 推送完成信号
                SseEmitter.SseEventBuilder doneEvent = SseEmitter.event()
                    .data("{\"status\":\"done\"}")
                    .name("done");
                emitter.send(doneEvent);
                emitter.complete();
                // 用完即删，防止内存泄漏
                uploadFileCache.remove(token);
            } catch (Exception e) {
                try {
                    String errMsg = e.getMessage();
                    if (errMsg == null || errMsg.isEmpty()) {
                        errMsg = "未知错误";
                    }
                    SseEmitter.SseEventBuilder event = SseEmitter.event()
                        .data("{\"status\":\"error\",\"msg\":\"文件处理失败: " + errMsg + "\"}")
                        .name("error");
                    emitter.send(event);
                } catch (IOException ioException) {
                    // ignore
                }
                emitter.complete();
            }
        }).start();
    
        emitter.onTimeout(() -> {
            try {
                emitter.send(SseEmitter.event().data("{\"status\":\"error\",\"msg\":\"SSE连接超时\"}").name("error"));
            } catch (IOException ignored) {}
            emitter.complete();
        });
        emitter.onError((throwable) -> {
            try {
                emitter.send(SseEmitter.event().data("{\"status\":\"error\",\"msg\":\"SSE连接出错\"}").name("error"));
            } catch (IOException ignored) {}
            emitter.complete();
        });
    
        return emitter;
    }
    // 新增：获取电台专辑详情（JSON接口，供前端批量获取，自动分页获取所有歌曲名）
    @GetMapping("/api/radio/album/{albumId}")
    @ResponseBody
    public Map<String, Object> getRadioAlbumDetailJson(
            @PathVariable String albumId,
            @RequestParam(value = "albumMid", required = false) String albumMid,
            @RequestParam(value = "pageSize", defaultValue = "100") int pageSize,
            @RequestParam(value = "sortBy", defaultValue = "-3") int sortBy,
            @RequestParam(value = "favState", defaultValue = "1") int favState
    ) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 固定每页 50 条（API 限制）
            final int realPageSize = 50;
            int page = 0;
            Map<String, Object> detail = qqMusicService.getRadioAlbumDetail(albumId, albumMid, page, realPageSize, sortBy, favState);
            result.put("albumId", albumId);
            result.put("cover", detail.getOrDefault("album_cover", detail.getOrDefault("cover", "")));
            Object totalNum = detail.get("total_num");
            Object songTotal = detail.get("songTotal");
            int songCount = 0;
            if (totalNum instanceof Number) {
                songCount = ((Number) totalNum).intValue();
            } else if (songTotal instanceof Number) {
                songCount = ((Number) songTotal).intValue();
            }
            if (songCount <= 0) {
                // 兜底
                Object songListObj = detail.get("songlist");
                if (songListObj instanceof List) {
                    songCount = ((List<?>) songListObj).size();
                }
            }
            // 分页循环获取所有歌曲名
            List<String> songNames = new ArrayList<>();
            int fetched = 0;
            while (fetched < songCount) {
                Map<String, Object> pageDetail;
                if (fetched == 0) {
                    pageDetail = detail;
                } else {
                    int curPage = fetched / realPageSize;
                    pageDetail = qqMusicService.getRadioAlbumDetail(albumId, albumMid, curPage, realPageSize, sortBy, favState);
                }
                Object songListObj = pageDetail.get("songlist");
                if (songListObj instanceof List) {
                    List<?> songList = (List<?>) songListObj;
                    for (Object songObj : songList) {
                        if (songObj instanceof Map) {
                            Object name = ((Map<?, ?>) songObj).get("song_name");
                            if (name != null) {
                                songNames.add(name.toString());
                            }
                        }
                    }
                    fetched += songList.size();
                    if (songList.size() < realPageSize) {
                        // 最后一页
                        break;
                    }
                } else {
                    break;
                }
            }
            result.put("songCount", songCount);
            result.put("songNames", songNames);
            result.put("ret", 0);
        } catch (Exception e) {
            result.put("ret", -1);
            result.put("msg", "获取专辑详情失败: " + e.getMessage());
        }
        return result;
    }
}