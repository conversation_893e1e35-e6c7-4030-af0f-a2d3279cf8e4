package com.ywcx.java_qqmusic_demo;

import org.springframework.stereotype.Service;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.math.BigDecimal;

@Service
public class QQMusicService {
    
    private static final Logger logger = LoggerFactory.getLogger(QQMusicService.class);
    
    private QQMusicClient qqMusicClient;
    private String qrCodeUrl;
    private String qrCodePath; // 本地二维码图片路径
    private String openId;
    private String accessToken;
    private String opiDeviceId;
    private String opiDeviceKey;
    private boolean loggedIn = false;
    private String authCode;
    
    public QQMusicService() {
        logger.info("[QQMusicService] 初始化服务开始");
        
        // 初始化QQ音乐客户端
        qqMusicClient = new QQMusicClient();
        logger.info("[QQMusicService] QQ音乐客户端初始化完成");
        
        // 异步获取二维码
        CompletableFuture.runAsync(this::initializeLogin);
        logger.info("[QQMusicService] 异步登录初始化任务已启动");
    }
    
    private void initializeLogin() {
        logger.info("[初始化登录] 开始登录流程");
        
        try {
            // 获取设备票据
            logger.info("[初始化登录] 开始获取设备票据");
            Map<String, String> deviceToken = qqMusicClient.getDeviceToken();
            if (deviceToken == null) {
                logger.error("[初始化登录] 获取设备票据失败");
                return;
            }

            opiDeviceId = deviceToken.get("opi_device_id");
            opiDeviceKey = deviceToken.get("opi_device_key");
            logger.info("[初始化登录] 设备票据获取成功 - deviceId: {}, deviceKey: {}", opiDeviceId, opiDeviceKey);

            // 获取登录二维码
            logger.info("[初始化登录] 开始获取登录二维码");
            Map<String, String> loginResult = qqMusicClient.getLoginQrCode(opiDeviceId, opiDeviceKey);
            if (loginResult == null) {
                logger.error("[初始化登录] 获取登录二维码失败");
                return;
            }

            authCode = loginResult.get("auth_code");
            qrCodeUrl = loginResult.get("qr_url");
            logger.info("[初始化登录] 二维码获取成功 - authCode: {}, qrUrl: {}", authCode, qrCodeUrl);
            
            // 获取本地二维码图片路径
            qrCodePath = "/qr-code"; // 修改为Web访问路径
            logger.debug("[初始化登录] 二维码访问路径设置为: {}", qrCodePath);

            // 轮询授权结果
            logger.info("[初始化登录] 开始轮询授权结果");
            String encryptString = qqMusicClient.pollAuthResult(authCode, opiDeviceId, opiDeviceKey);
            if (encryptString == null) {
                logger.error("[初始化登录] 轮询授权结果超时或失败");
                return;
            }
            logger.info("[初始化登录] 授权成功，获得加密字符串");

            // 获取访问令牌
            logger.info("[初始化登录] 开始获取访问令牌");
            Map<String, String> tokenInfo = qqMusicClient.getAccessToken(encryptString, opiDeviceId, opiDeviceKey);
            // 如果获取访问令牌失败（例如，因为refresh_token过期），提示用户重新登录
            while (tokenInfo == null) {
                logger.warn("[初始化登录] 访问令牌获取失败，重新开始登录流程");
                
                loginResult = qqMusicClient.getLoginQrCode(opiDeviceId, opiDeviceKey);
                if (loginResult == null) {
                    logger.error("[初始化登录] 无法生成新的登录二维码，退出登录流程");
                    return;
                }

                authCode = loginResult.get("auth_code");
                qrCodeUrl = loginResult.get("qr_url");
                logger.info("[初始化登录] 重新生成二维码 - authCode: {}", authCode);
                
                encryptString = qqMusicClient.pollAuthResult(authCode, opiDeviceId, opiDeviceKey);
                if (encryptString == null) {
                    logger.error("[初始化登录] 重新轮询授权失败，退出登录流程");
                    return;
                }

                tokenInfo = qqMusicClient.getAccessToken(encryptString, opiDeviceId, opiDeviceKey);
            }

            openId = tokenInfo.get("open_id");
            accessToken = tokenInfo.get("access_token");
            loggedIn = true;
            
            logger.info("[初始化登录] 登录成功 - openId: {}, accessToken: {}...", openId, accessToken.substring(0, Math.min(10, accessToken.length())));
        } catch (Exception e) {
            logger.error("[初始化登录] 登录流程异常", e);
        }
    }
    
    public String getQrCodeUrl() {
        return qrCodePath; // 返回Web访问路径
    }
    
    public boolean isLoggedIn() {
        return loggedIn;
    }
    
    public Map<String, Object> searchMusic(String query, int searchType, int pageNo, int pageSize) {
        logger.info("[搜索音乐] 入参 - query: {}, searchType: {}, pageNo: {}, pageSize: {}", query, searchType, pageNo, pageSize);
        
        if (!loggedIn) {
            logger.warn("[搜索音乐] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.searchMusic(query, searchType, pageNo, pageSize, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[搜索音乐] 返回结果 - ret: {}, 数据长度: {}", result.get("ret"), result.toString().length());
            return result;
        } catch (Exception e) {
            logger.error("[搜索音乐] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> searchSonglist(String query, int pageNo, int pageSize) {
        logger.info("[搜索歌单] 入参 - query: {}, pageNo: {}, pageSize: {}", query, pageNo, pageSize);
        
        if (!loggedIn) {
            logger.warn("[搜索歌单] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.searchSonglist(query, pageNo, pageSize, openId, accessToken, opiDeviceId, opiDeviceKey);
            
            // 统一处理数值类型
            if (result.containsKey("total")) {
                Object totalObj = result.get("total");
                if (totalObj instanceof Number) {
                    BigDecimal total = BigDecimal.valueOf(((Number)totalObj).doubleValue());
                    result.put("total", total);
                    logger.debug("[搜索歌单] 数值类型转换 - total: {}", total);
                }
            }
            
            logger.info("[搜索歌单] 返回结果 - ret: {}, total: {}", result.get("ret"), result.get("total"));
            return result;
        } catch (Exception e) {
            logger.error("[搜索歌单] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getSonglistDetail(String dissid, int page, int pageSize) {
        logger.info("[获取歌单详情] 入参 - dissid: {}, page: {}, pageSize: {}", dissid, page, pageSize);
        
        if (!loggedIn) {
            logger.warn("[获取歌单详情] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.getSonglistDetail(dissid, page, pageSize, openId, accessToken, opiDeviceId, opiDeviceKey);
            
            // 映射统一字段，兼容前端模板 songlist-detail.html
            if (result.containsKey("songTotal")) {
                Object totalObj = result.get("songTotal");
                if (totalObj instanceof Number) {
                    BigDecimal songTotal = BigDecimal.valueOf(((Number)totalObj).doubleValue());
                    result.put("total_num", songTotal); // 前端需要 total_num
                    logger.debug("[获取歌单详情] 数值类型转换 - total_num: {}", songTotal);
                }
            }
            // 将 songlist 转换为 song_list，前端模版要求
            if (result.containsKey("songlist") && !result.containsKey("song_list")) {
                result.put("song_list", result.get("songlist"));
            }
            
            logger.info("[获取歌单详情] 返回结果 - ret: {}, total_num: {}", result.get("ret"), result.get("total_num"));
            return result;
        } catch (Exception e) {
            logger.error("[获取歌单详情] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getSongInfoBatch(List<String> songMids) {
        logger.info("[批量获取歌曲信息] 入参 - songMids: {}", songMids);
        
        if (!loggedIn) {
            logger.warn("[批量获取歌曲信息] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.getSongInfoBatch(songMids, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[批量获取歌曲信息] 返回结果 - ret: {}", result.get("ret"));
            return result;
        } catch (Exception e) {
            logger.error("[批量获取歌曲信息] 异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取单首歌曲播放URL
     */
    public String getSongPlayUrl(String songMid) {
        logger.info("[获取播放URL] 入参 - songMid: {}", songMid);
        if (!loggedIn) {
            logger.warn("[获取播放URL] 用户未登录，返回null");
            return null;
        }
        try {
            String url = qqMusicClient.getSongPlayUrl(songMid, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[获取播放URL] 返回URL: {}", url);
            return url;
        } catch (Exception e) {
            logger.error("[获取播放URL] 异常", e);
            return null;
        }
    }

    /**
     * 批量获取歌曲播放URL
     */
    public Map<String, String> getSongPlayUrlBatch(List<String> songMids) {
        logger.info("[批量获取播放URL] 入参 - songMids: {}", songMids);
        if (!loggedIn) {
            logger.warn("[批量获取播放URL] 用户未登录，返回空Map");
            return new HashMap<>();
        }
        try {
            Map<String, String> result = qqMusicClient.getSongPlayUrlBatch(songMids, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[批量获取播放URL] 返回结果: {}", result);
            return result;
        } catch (Exception e) {
            logger.error("[批量获取播放URL] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getLyric(String songMid) {
        logger.info("[获取歌词] 入参 - songMid: {}", songMid);
        
        if (!loggedIn) {
            logger.warn("[获取歌词] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.getLyric(songMid, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[获取歌词] 返回结果 - ret: {}", result.get("ret"));
            return result;
        } catch (Exception e) {
            logger.error("[获取歌词] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getSingerAvatar(String singerMid) {
        logger.info("[获取歌手头像] 入参 - singerMid: {}", singerMid);
        
        if (!loggedIn) {
            logger.warn("[获取歌手头像] 用户未登录，返回空结果");
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = qqMusicClient.getSingerAvatar(singerMid, openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("[获取歌手头像] 返回结果 - ret: {}", result.get("ret"));
            return result;
        } catch (Exception e) {
            logger.error("[获取歌手头像] 异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getRadioCategoryInfo() {
        if (!loggedIn) {
            logger.warn("用户未登录，无法获取电台分类信息");
            return new HashMap<>();
        }
        
        try {
            logger.info("开始获取电台分类信息");
            Map<String, Object> result = qqMusicClient.getRadioCategoryInfo(openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("获取电台分类信息完成，返回结果: {}", result);
            return result;
        } catch (Exception e) {
            logger.error("获取电台分类信息时发生异常", e);
            return new HashMap<>();
        }
    }
    
    public Map<String, Object> getRadioCategoryAlbumList(int fstId, int sndId, int sortType, int begin, int number) {
        if (!loggedIn) {
            logger.warn("用户未登录，无法获取电台分类专辑列表");
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "用户未登录");
            return errorResult;
        }
        
        try {
            logger.info("开始获取电台分类专辑列表，参数: fstId={}, sndId={}, sortType={}, begin={}, number={}",
                       fstId, sndId, sortType, begin, number);
            Map<String, Object> result = qqMusicClient.getRadioCategoryAlbumList(fstId, sndId, sortType, begin, number,
                    openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("获取电台分类专辑列表完成，结果: {}", result);
            return result != null ? result : new HashMap<>();
        } catch (Exception e) {
            logger.error("获取电台分类专辑列表时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "系统错误: " + e.getMessage());
            return errorResult;
        }
    }
    
    public Map<String, Object> getRadioAlbumDetail(String albumId, String albumMid, int page, int pageSize, int sortBy, int favState) {
        if (!loggedIn) {
            logger.warn("用户未登录，无法获取电台专辑详情");
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "用户未登录");
            return errorResult;
        }
        
        try {
            logger.info("开始获取电台专辑详情，参数: albumId={}, albumMid={}, page={}, pageSize={}, sortBy={}, favState={}",
                       albumId, albumMid, page, pageSize, sortBy, favState);
            Map<String, Object> result = qqMusicClient.getRadioAlbumDetail(albumId, albumMid, page, pageSize, sortBy, favState,
                    openId, accessToken, opiDeviceId, opiDeviceKey);
            logger.info("获取电台专辑详情完成");
            return result != null ? result : new HashMap<>();
        } catch (Exception e) {
            logger.error("获取电台专辑详情时发生异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("ret", -1);
            errorResult.put("msg", "系统错误: " + e.getMessage());
            return errorResult;
        }
    }
    
    // 保持向后兼容的重载方法
    public Map<String, Object> getRadioAlbumDetail(String albumId, int page, int pageSize) {
        return getRadioAlbumDetail(albumId, null, page, pageSize, -3, 1);
    }

    public static class Songlist {
        private String dissid;
        private String dissname;
        private String logo;
        private String nickname;
        private int songnum;
        
        // Getters and setters
        public String getDissid() {
            return dissid;
        }
        
        public void setDissid(String dissid) {
            this.dissid = dissid;
        }
        
        public String getDissname() {
            return dissname;
        }
        
        public void setDissname(String dissname) {
            this.dissname = dissname;
        }
        
        public String getLogo() {
            return logo;
        }
        
        public void setLogo(String logo) {
            this.logo = logo;
        }
        
        public String getNickname() {
            return nickname;
        }
        
        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
        
        public int getSongnum() {
            return songnum;
        }
        
        public void setSongnum(int songnum) {
            this.songnum = songnum;
        }
    }
    
    public static class Song {
        private String songName;
        private String singerName;
        
        public Song(String songName, String singerName) {
            this.songName = songName;
            this.singerName = singerName;
        }
        
        // Getters and setters
        public String getSongName() {
            return songName;
        }
        
        public void setSongName(String songName) {
            this.songName = songName;
        }
        
        public String getSingerName() {
            return singerName;
        }
        
        public void setSingerName(String singerName) {
            this.singerName = singerName;
        }
    }
}